#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
医疗病历自动化处理系统 - 核心功能示例实现
基于抓包分析的API调用实现

Author: 开发团队
Date: 2025-08-19
"""

import asyncio
import json
import base64
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import httpx
import xml.etree.ElementTree as ET
from urllib.parse import urlencode


@dataclass
class AuthToken:
    """认证令牌数据类"""
    access_token: str
    refresh_token: str
    expires_in: int
    user_id: str
    user_name: str
    created_at: datetime


@dataclass
class PatientInfo:
    """患者信息数据类"""
    patient_no: str
    name: str
    gender: str
    age: int
    admission_no: str
    department: str
    diagnosis: str
    bed_no: str


@dataclass
class TemplateInfo:
    """模板信息数据类"""
    template_id: str
    template_name: str
    template_code: str
    file_id: str
    course_type: str


class AuthManager:
    """认证管理器 - 处理OAuth2认证和Token管理"""
    
    def __init__(self, base_url: str, client_id: str, client_secret: str):
        self.base_url = base_url
        self.client_id = client_id
        self.client_secret = client_secret
        self.current_token: Optional[AuthToken] = None
        
    def _encode_credentials(self) -> str:
        """编码客户端凭据"""
        credentials = f"{self.client_id}:{self.client_secret}"
        return base64.b64encode(credentials.encode()).decode()
    
    async def authenticate_client(self) -> str:
        """客户端认证 - 第一步认证"""
        url = f"{self.base_url}/authserver/oauth/token"
        headers = {
            "Authorization": f"Basic {self._encode_credentials()}",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        data = "grant_type=client_credentials"
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, content=data)
            response.raise_for_status()
            
            result = response.json()
            return result["access_token"]
    
    async def authenticate_user(self, user_id: str, client_token: str) -> AuthToken:
        """用户认证 - 第二步认证"""
        url = f"{self.base_url}/manager/auth-ext/auth"
        headers = {
            "Authorization": f"Bearer {client_token}",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        # 构建认证数据
        basic_auth = f"Basic {self._encode_credentials()}"
        data = f"authType=ekinghis_login&basic={urlencode({'': basic_auth})[1:]}&cred={user_id}"
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, content=data)
            response.raise_for_status()
            
            result = response.json()
            data_info = result["data"]["dataInfo"]
            
            self.current_token = AuthToken(
                access_token=data_info["access_token"],
                refresh_token=data_info["refresh_token"],
                expires_in=data_info["expires_in"],
                user_id=data_info["userId"],
                user_name=data_info["userName"],
                created_at=datetime.now()
            )
            
            return self.current_token
    
    def is_token_valid(self) -> bool:
        """检查令牌是否有效"""
        if not self.current_token:
            return False
        
        expires_at = self.current_token.created_at + timedelta(seconds=self.current_token.expires_in)
        return datetime.now() < expires_at - timedelta(minutes=5)  # 提前5分钟刷新
    
    def get_auth_headers(self) -> Dict[str, str]:
        """获取认证头"""
        if not self.current_token:
            raise ValueError("No valid token available")
        
        return {
            "Authorization": f"Bearer {self.current_token.access_token}",
            "ek-b-clientversion": "1.3.6.399",
            "ek-b-clientip": "***********",
            "ek-b-logintype": ""
        }


class PatientManager:
    """患者信息管理器"""
    
    def __init__(self, base_url: str, auth_manager: AuthManager):
        self.base_url = base_url
        self.auth_manager = auth_manager
    
    async def get_patient_info(self, patient_no: str, visit_id: str, doc_id: str, id_no: str) -> PatientInfo:
        """获取患者基本信息"""
        # 构建查询参数
        params = {
            "patNo": patient_no,
            "visitTime": "1",
            "visit_id": visit_id,
            "doc_id": doc_id,
            "idNo": id_no
        }
        
        url = f"{self.base_url}/manager/processToken/test_process_data"
        url += f"?funCode=GetPATINFO&funParams={urlencode({'': json.dumps(params)})[1:]}"
        
        headers = self.auth_manager.get_auth_headers()
        headers["Content-Length"] = "0"
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            patient_data = result["data"]["dataInfo"]["result"][0]
            
            return PatientInfo(
                patient_no=patient_data["ZYH"],
                name=patient_data["XM"],
                gender=patient_data["XB"],
                age=patient_data["NL"],
                admission_no=patient_data["ZYH"],
                department=patient_data["JZKS"],
                diagnosis=patient_data["ZD"],
                bed_no=patient_data["CH"]
            )


class TemplateManager:
    """病历模板管理器"""
    
    def __init__(self, base_url: str, auth_manager: AuthManager):
        self.base_url = base_url
        self.auth_manager = auth_manager
    
    async def get_template_list(self, dept_code: str, creator_no: str, visit_id: str, 
                               doc_type: int = 2, template_type: int = 2) -> List[TemplateInfo]:
        """获取病历模板列表"""
        url = f"{self.base_url}/emr/index/tpl"
        params = {
            "deptCode": dept_code,
            "createrNo": creator_no,
            "visitId": visit_id,
            "docType": doc_type,
            "type": template_type
        }
        
        headers = self.auth_manager.get_auth_headers()
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params, headers=headers)
            response.raise_for_status()
            
            result = response.json()
            templates = []
            
            for template_data in result["data"]["dataInfo"]:
                templates.append(TemplateInfo(
                    template_id=template_data["tplId"],
                    template_name=template_data["tplName"],
                    template_code=template_data["tplCode"],
                    file_id=template_data["fileId"],
                    course_type=template_data["courseType"]
                ))
            
            return templates
    
    async def get_template_content(self, file_id: str) -> str:
        """获取模板XML内容"""
        url = f"{self.base_url}/oss/file/show"
        params = {"fid": file_id}
        headers = self.auth_manager.get_auth_headers()
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params, headers=headers)
            response.raise_for_status()
            
            return response.text
    
    def parse_template_fields(self, xml_content: str) -> Dict[str, Dict[str, Any]]:
        """解析模板中的FC Code字段"""
        root = ET.fromstring(xml_content)
        fields = {}
        
        # 查找所有InputField元素
        for element in root.iter():
            if element.tag.endswith('InputField'):
                field_code = None
                field_name = None
                field_type = None
                
                # 解析属性
                attributes = element.find('.//Attributes')
                if attributes is not None:
                    for attr in attributes.findall('Attribute'):
                        name_elem = attr.find('Name')
                        value_elem = attr.find('Value')
                        
                        if name_elem is not None and value_elem is not None:
                            if name_elem.text == 'FieldCode':
                                field_code = value_elem.text
                            elif name_elem.text == 'FieldName':
                                field_name = value_elem.text
                            elif name_elem.text == 'FieldDataType':
                                field_type = value_elem.text
                
                if field_code:
                    fields[field_code] = {
                        'field_name': field_name,
                        'field_type': field_type,
                        'element': element
                    }
        
        return fields


class LLMIntegrator:
    """LLM集成器 - 处理AI内容生成"""
    
    def __init__(self, llm_api_key: str, model: str = "gpt-4"):
        self.api_key = llm_api_key
        self.model = model
        self.prompt_templates = {
            "FC0000001113": "根据患者{name}，{age}岁{gender}，诊断为{diagnosis}，生成合适的主诉内容，要求简洁明了，50字以内。",
            "FC0000000850": "基于患者{name}，{age}岁{gender}，主诉：{chief_complaint}，诊断{diagnosis}，生成详细的现病史，500字以内。",
            "FC0000001144": "患者{name}，{age}岁{gender}，住院号{admission_no}，诊断{diagnosis}，生成主治医师查房记录，300字以内。"
        }
    
    async def generate_content(self, field_code: str, patient_info: PatientInfo, context: Dict[str, Any] = None) -> str:
        """根据字段代码生成内容"""
        if field_code not in self.prompt_templates:
            return ""
        
        # 构建提示词
        template = self.prompt_templates[field_code]
        prompt_context = {
            "name": patient_info.name,
            "age": patient_info.age,
            "gender": patient_info.gender,
            "diagnosis": patient_info.diagnosis,
            "admission_no": patient_info.admission_no,
            "department": patient_info.department
        }
        
        if context:
            prompt_context.update(context)
        
        prompt = template.format(**prompt_context)
        
        # 这里应该调用实际的LLM API
        # 为了示例，返回模拟内容
        if field_code == "FC0000001113":  # 主诉
            return f"记忆力下降{patient_info.age//10}年，加重1周"
        elif field_code == "FC0000000850":  # 现病史
            return f"患者{patient_info.name}，{patient_info.age}岁{patient_info.gender}，因记忆力下降就诊。近年来记忆力逐渐下降，近1周来明显加重，表现为近事遗忘，计算力下降，定向力障碍。无头痛、头晕，无恶心呕吐，无肢体活动障碍。既往体健，否认高血压、糖尿病病史。"
        elif field_code == "FC0000001144":  # 查房记录
            return f"患者{patient_info.name}，{patient_info.age}岁{patient_info.gender}，因{patient_info.diagnosis}入院治疗。目前患者神志清楚，精神状态尚可，生命体征平稳。继续现有治疗方案，密切观察病情变化。"
        
        return "AI生成内容"


# 使用示例
async def main():
    """主程序示例"""
    # 配置信息
    BASE_URL = "http://11.0.10.25:4431"
    CLIENT_ID = "ekinghis-doctor-emr"
    CLIENT_SECRET = "123"
    USER_ID = "24121605"
    
    try:
        # 1. 认证
        auth_manager = AuthManager(BASE_URL, CLIENT_ID, CLIENT_SECRET)
        
        print("正在进行客户端认证...")
        client_token = await auth_manager.authenticate_client()
        
        print("正在进行用户认证...")
        user_token = await auth_manager.authenticate_user(USER_ID, client_token)
        print(f"认证成功，用户: {user_token.user_name}")
        
        # 2. 获取患者信息
        patient_manager = PatientManager(BASE_URL, auth_manager)
        print("正在获取患者信息...")
        
        # 这里需要实际的患者参数
        # patient_info = await patient_manager.get_patient_info("574703", "1148974458015318016", "1148979974485573632", "410611195810206029")
        
        # 3. 获取模板列表
        template_manager = TemplateManager(BASE_URL, auth_manager)
        print("正在获取模板列表...")
        
        # templates = await template_manager.get_template_list("200401", "5747", "1140241962675208192")
        
        print("系统初始化完成，可以开始处理病历")
        
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    asyncio.run(main())
