# FC Code提示词策略设计清单

## 1. 病程记录相关FC Code字段分析

### 1.1 首次病程记录FC Code字段

| FC Code | 字段名称 | 内容要求 | 格式规范 | LLM生成需求 |
|---------|----------|----------|----------|-------------|
| FC0000001144 | 病程记录内容 | 详细描述患者病情、诊断、治疗计划 | 300-500字，结构化医疗记录 | 是 |
| FC0000001100 | 记录日期时间 | 当前日期时间 | yyyy年MM月dd日 HH时mm分 | 否（系统生成） |
| FC0000001147 | 记录人签名 | 医生姓名 | 文本格式 | 否（用户信息） |
| FC0000001159 | 主治医师签名 | 主治医师姓名 | 文本格式 | 否（用户信息） |
| FC0000001099 | 患者姓名 | 患者真实姓名 | 文本格式 | 否（患者信息） |
| FC0000001108 | 性别代码 | 性别信息 | 男/女 | 否（患者信息） |
| FC0000001105 | 年龄(岁) | 患者年龄 | 数字格式 | 否（患者信息） |
| FC0000001114 | 住院号 | 住院编号 | 数字格式 | 否（患者信息） |

### 1.2 主治医师查房记录FC Code字段

| FC Code | 字段名称 | 内容要求 | 格式规范 | LLM生成需求 |
|---------|----------|----------|----------|-------------|
| FC0000001144 | 查房记录内容 | 病情评估、治疗效果、下一步计划 | 200-400字，结构化记录 | 是 |
| FC0000001145 | 查房日期时间 | 查房时间 | yyyy年MM月dd日 HH时mm分 | 否（系统生成） |
| FC0000001159 | 主治医师签名 | 主治医师姓名 | 文本格式 | 否（用户信息） |
| FC0000001099 | 患者姓名 | 患者真实姓名 | 文本格式 | 否（患者信息） |
| FC0000001108 | 性别代码 | 性别信息 | 男/女 | 否（患者信息） |
| FC0000001105 | 年龄(岁) | 患者年龄 | 数字格式 | 否（患者信息） |

### 1.3 主任医师查房记录FC Code字段

| FC Code | 字段名称 | 内容要求 | 格式规范 | LLM生成需求 |
|---------|----------|----------|----------|-------------|
| FC0000001144 | 查房记录内容 | 高级医疗决策、疑难问题分析 | 300-600字，专业性更强 | 是 |
| FC0000001145 | 查房日期时间 | 查房时间 | yyyy年MM月dd日 HH时mm分 | 否（系统生成） |
| FC0000001107 | 上级医师审签 | 主任医师姓名 | 文本格式 | 否（用户信息） |
| FC0000001159 | 主治医师签名 | 主治医师姓名 | 文本格式 | 否（用户信息） |

### 1.4 日常病程记录FC Code字段

| FC Code | 字段名称 | 内容要求 | 格式规范 | LLM生成需求 |
|---------|----------|----------|----------|-------------|
| FC0000001144 | 病程记录内容 | 日常病情变化、治疗调整 | 150-300字，简洁明了 | 是 |
| FC0000001100 | 记录日期时间 | 记录时间 | yyyy年MM月dd日 HH时mm分 | 否（系统生成） |
| FC0000001147 | 记录人签名 | 记录医生姓名 | 文本格式 | 否（用户信息） |

## 2. FC Code级别的精确提示词设计

### 2.1 FC0000001144 - 病程记录内容（首次病程记录）

**字段要求**：
- 内容长度：300-500字
- 必须包含：入院诊断、病情评估、治疗计划、观察要点
- 医疗术语准确，逻辑清晰

**专用提示词模板**：
```
你是一名经验丰富的临床医生，正在为患者编写首次病程记录。

患者基本信息：
- 姓名：{patient_name}，{gender}，{age}岁
- 住院号：{admission_no}
- 科室：{department}
- 入院诊断：{admission_diagnosis}
- 入院时间：{admission_time}

医生输入的关键信息：
{doctor_inputs}

请按照以下结构生成首次病程记录内容：

1. 病情评估（100-150字）：
   - 患者当前主要症状和体征
   - 生命体征和一般情况
   - 重要检查结果

2. 诊断分析（50-100字）：
   - 确认入院诊断的依据
   - 需要鉴别的疾病

3. 治疗计划（100-150字）：
   - 具体的治疗措施
   - 用药方案
   - 检查安排

4. 观察要点（50-100字）：
   - 需要密切观察的指标
   - 可能出现的并发症

要求：
- 总字数控制在300-500字
- 使用规范的医学术语
- 逻辑清晰，条理分明
- 符合首次病程记录的医疗规范

请生成病程记录内容：
```

### 2.2 FC0000001144 - 查房记录内容（主治医师查房）

**字段要求**：
- 内容长度：200-400字
- 必须包含：病情评估、治疗效果、下一步计划
- 体现主治医师的专业判断

**专用提示词模板**：
```
你是一名主治医师，正在进行查房并记录查房内容。

患者信息：
- 姓名：{patient_name}，{gender}，{age}岁
- 住院天数：{days_hospitalized}天
- 当前诊断：{current_diagnosis}

病情变化：
{condition_changes}

治疗情况：
{current_treatment}

最新检查结果：
{recent_examinations}

请按照以下结构生成主治医师查房记录：

1. 病情评估（80-120字）：
   - 患者当前病情状态
   - 与前次查房的变化对比
   - 治疗反应情况

2. 治疗效果分析（60-100字）：
   - 现有治疗方案的效果
   - 存在的问题和不足

3. 下一步计划（60-120字）：
   - 治疗方案的调整
   - 新的检查安排
   - 预期目标

要求：
- 总字数控制在200-400字
- 体现主治医师的专业水平
- 重点突出，针对性强
- 符合查房记录规范

请生成查房记录内容：
```

### 2.3 FC0000001144 - 查房记录内容（主任医师查房）

**字段要求**：
- 内容长度：300-600字
- 必须包含：高级医疗决策、疑难问题分析、指导意见
- 体现主任医师的权威性和专业深度

**专用提示词模板**：
```
你是一名主任医师，正在进行高级查房并提供专业指导。

患者复杂情况：
- 姓名：{patient_name}，{gender}，{age}岁
- 复杂诊断：{complex_diagnosis}
- 治疗难点：{treatment_challenges}
- 会诊意见：{consultation_opinions}

疑难问题：
{difficult_issues}

下级医师汇报：
{junior_doctor_report}

请按照以下结构生成主任医师查房记录：

1. 病情综合分析（120-200字）：
   - 疾病的复杂性分析
   - 多系统问题的关联性
   - 预后评估

2. 疑难问题解答（100-200字）：
   - 诊断难点的分析
   - 治疗选择的依据
   - 风险效益评估

3. 指导意见（80-200字）：
   - 治疗方案的优化
   - 下级医师的培训指导
   - 质量控制要求

要求：
- 总字数控制在300-600字
- 体现主任医师的学术水平
- 具有指导和教学价值
- 符合高级查房记录规范

请生成主任医师查房记录内容：
```

### 2.4 FC0000001144 - 病程记录内容（日常病程记录）

**字段要求**：
- 内容长度：150-300字
- 必须包含：病情变化、治疗调整、观察结果
- 简洁明了，重点突出

**专用提示词模板**：
```
你是一名住院医师，正在记录患者的日常病程变化。

患者信息：
- 姓名：{patient_name}，{gender}，{age}岁
- 住院第{day_number}天
- 主要诊断：{main_diagnosis}

今日病情：
{today_condition}

治疗执行情况：
{treatment_execution}

观察结果：
{observation_results}

请按照以下结构生成日常病程记录：

1. 病情变化（60-100字）：
   - 症状的改善或加重
   - 体征的变化
   - 患者主观感受

2. 治疗执行（40-80字）：
   - 药物治疗的执行
   - 其他治疗措施的落实
   - 患者配合情况

3. 观察要点（50-120字）：
   - 重要指标的监测结果
   - 需要关注的问题
   - 下一步观察重点

要求：
- 总字数控制在150-300字
- 重点突出，避免冗余
- 客观记录，准确描述
- 符合日常病程记录规范

请生成日常病程记录内容：
```

## 3. 提示词优化策略

### 3.1 上下文动态调整
- 根据患者病情严重程度调整提示词的紧急性表述
- 根据住院天数调整记录的重点内容
- 根据科室特点调整专业术语的使用

### 3.2 质量控制机制
- 内容长度自动检查
- 必要元素完整性验证
- 医学术语准确性评估
- 逻辑一致性检查

### 3.3 个性化定制
- 支持医生个人风格的提示词调整
- 科室特色的模板定制
- 疾病类型的专用提示词

## 4. 审核和修改建议

请审核以上FC Code提示词策略设计，特别关注：

1. **字段覆盖完整性**：是否遗漏了重要的FC Code字段？
2. **提示词专业性**：医学术语和表述是否准确？
3. **内容要求合理性**：字数限制和格式要求是否合适？
4. **差异化设计**：不同记录类型的提示词是否有足够的区分度？
5. **实用性评估**：提示词是否能够指导LLM生成高质量的医疗内容？

请提供您的修改意见和补充建议。
