0.019058s
POST /oss/file/upload HTTP/1.1
ek-b-clientversion: 1.3.6.399
ek-b-clientip: ***********
ek-b-logintype: 
Authorization: Bearer ey<PERSON>hbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jA_u9aw-SmIDxfZ6dh4v9Xpzz7Bl7DQFeiBZ0RYWXBp3QGcDJYAyWpPw4uUQxaLlB4jWPJa_ZNK5sjLCvw8_O_1l3f6-z7Pn3AkyonEy-EC7X3W9w4ZUl4xiPhit53XpLMaSP8GvKwlUpE9-z0sdgeIsdsnXzy-saOLX-uNJj8s
Content-Type: multipart/form-data; boundary="----MyGreatBoundary"
Host: **********:4431
Content-Length: 14238
Expect: 100-continue


0.001067s
HTTP/1.1 100 Continue


0.002554s
------MyGreatBoundary
Content-Type: text/plain; charset=utf-8
Content-Disposition: form-data; name=fid


------MyGreatBoundary
Content-Disposition: form-data; name=file; filename=f087b80bd78541ec85b8cdb51b6aff66.xml; filename*=utf-8''f087b80bd78541ec85b8cdb51b6aff66.xml

﻿<?xml version="1.0" encoding="utf-8"?>
<XTextDocument xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" EditorVersionString="DCWriter20250619152910">
   <Attributes>
      <Attribute>
         <Name>科室</Name>
         <Value>神经内科总院</Value>
      </Attribute>
      <Attribute>
         <Name>科室Code</Name>
         <Value>200401</Value>
      </Attribute>
      <Attribute>
         <Name>病区</Name>
         <Value>神经内科总院病区护士站</Value>
      </Attribute>
      <Attribute>
         <Name>床号</Name>
         <Value>28</Value>
      </Attribute>
   </Attributes>
   <InnerID>14</InnerID>
   <ContentReadonly>True</ContentReadonly>
   <XElements>
      <Element xsi:type="XTextHeader">
         <InnerID>106</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="XParagraphFlag" StyleIndex="0">
               <InnerID>111</InnerID>
               <AutoCreate>true</AutoCreate>
            </Element>
         </XElements>
      </Element>
      <Element xsi:type="XTextBody">
         <InnerID>107</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="XInputField">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>查房日期时间</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001145</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>DT</Value>
                  </Attribute>
                  <Attribute>
                     <Name>ShowTimeFlag</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>77</InnerID>
               <ID>field1</ID>
               <ToolTip>【查房日期时间】开始查房时的公元纪年日期和时间的完整描述</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000210</DataSource>
                  <BindingPath>CI00002256</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>120</InnerID>
                     <Text>2025年08月03日 17时48分</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <Name>FC0000001145</Name>
               <DisplayFormat>
                  <Style>DateTime</Style>
                  <Format>yyyy年MM月dd日 HH时mm分</Format>
               </DisplayFormat>
               <InnerValue>2025/8/3 17:48:43</InnerValue>
               <BackgroundText>yyyy年MM月dd日 HH时mm分</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings>
                  <EditStyle>DateTime</EditStyle>
               </FieldSettings>
            </Element>
            <Element xsi:type="XString" WhitespaceCount="7">
               <InnerID>116</InnerID>
               <Text>       </Text>
            </Element>
            <Element xsi:type="XString" StyleIndex="0" WhitespaceCount="1">
               <InnerID>117</InnerID>
               <Text> </Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="0">

               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>查房医师</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000009718</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignByDoctorFlag</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>InputModeParam</Name>
                     <Value>{"DataSourceCode":"WardRoundDoctors","FixedFilter":{"Relation":"OR","Filters":[{"FieldCode":"DeptCode","Operator":"EQ","Parameter":"{patient.deptCode}"}]},"Sort":[{"FieldCode":"StaffFlag"}]}</Value>
                  </Attribute>
               </Attributes>
               <InnerID>99</InnerID>
               <ID>field5</ID>
               <ToolTip>【查房医师】</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <Deleteable>false</Deleteable>
               <UserEditable>false</UserEditable>
               <Name>FC0000009718</Name>
               <BackgroundText>查房医师</BackgroundText>
               <EditorControlTypeName>EkHis.Components.Emr.Winform.View.AsyncInputControlForDCWriter</EditorControlTypeName>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="0">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>查房记录</Value>
                  </Attribute>
               </Attributes>
               <InnerID>626</InnerID>
               <ID>FC0000001144</ID>
               <Deleteable>false</Deleteable>
               <Name>0</Name>
               <Width>350.8545</Width>
               <Height>49.90234</Height>
               <Text>主治医师查房记录</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>633</InnerID>
            </Element>
            <Element xsi:type="XString">
               <InnerID>118</InnerID>
               <Text>这里由ai编写</Text>#提示词模板代号0021
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>375</InnerID>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="1">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>主治医师签名</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001159</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignLevel</Name>
                     <Value>EMR_AUDIT_ATTEND</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignVerify</Name>
                     <Value>2</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignByDoctorFlag</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>70</InnerID>
               <ID>field4</ID>
               <ToolTip>【主治医师签名】具有主治医师专业技术职务资格的医师签署的在公安户籍管理部门正式登记注册的姓氏和名称</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <Deleteable>false</Deleteable>
               <UserEditable>false</UserEditable>
               <Name>FC0000001159</Name>
               <BackgroundText>上级医师签名</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString" StyleIndex="1">
               <InnerID>119</InnerID>
               <Text>/</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="1">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>记录人签名</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001147</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignLevel</Name>
                     <Value>EMR_AUDIT_RESIDENT</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignVerify</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>1</Value>
                  </Attribute>
               </Attributes>
               <InnerID>62</InnerID>
               <ID>field3</ID>
               <ToolTip>【记录人签名】记录单填写者签署的在公安户籍管理部门正式登记注册的姓氏和名称</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <Deleteable>false</Deleteable>
               <Name>FC0000001147</Name>
               <BackgroundText>双击签名</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag" StyleIndex="1">
               <InnerID>69</InnerID>
            </Element>
         </XElements>
      </Element>
      <Element xsi:type="XTextFooter">
         <InnerID>108</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="XParagraphFlag">
               <InnerID>113</InnerID>
               <AutoCreate>true</AutoCreate>
            </Element>
         </XElements>
      </Element>
   </XElements>
   <Parameters>
      <Parameter Name="TI00000210" />
      <Parameter Name="TI00000206" />
      <Parameter Name="TI00000212" />
   </Parameters>
   <FileName>D:\ekingsoft\doctor5\ekingemr\tempdoc\f087b80bd78541ec85b8cdb51b6aff66.xml</FileName>
   <FileFormat>XML</FileFormat>
   <ContentStyles>
      <Default xsi:type="DocumentContentStyle">
         <FontName>宋体</FontName>
         <FontSize>10.5</FontSize>
      </Default>
      <Styles>
         <Style Index="0">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <Bold>true</Bold>
         </Style>
         <Style Index="1">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <Align>Right</Align>
         </Style>
      </Styles>
   </ContentStyles>
   <Info>
      <Title>08-03 17:48 主治医师查房记录</Title>
      <LicenseText>江苏鑫亿软件股份有限公司:鹤壁市人民医院</LicenseText>
      <CreationTime>1980-01-01T00:00:00</CreationTime>
      <LastModifiedTime>2025-08-03T17:57:27.1801258+08:00</LastModifiedTime>
      <LastPrintTime>1980-01-01T00:00:00</LastPrintTime>
      <Operator>DCSoft.Writer Version:1.2022.6.27</Operator>
      <NumOfPage>1</NumOfPage>
   </Info>
   <BodyText>2025年08月03日 17时48分        查房医师主治医师查房记录
这里由ai编写
上级医师签名/双击签名</BodyText>
   <LocalConfig />
   <PageSettings>
      <PaperKind>Custom</PaperKind>
      <PaperWidth>716</PaperWidth>
      <PaperHeight>1043</PaperHeight>
      <LeftMargin>35</LeftMargin>
      <TopMargin>164</TopMargin>
      <RightMargin>35</RightMargin>
      <BottomMargin>59</BottomMargin>
   </PageSettings>
</XTextDocument>
------MyGreatBoundary--

0.049302s
HTTP/1.1 200 
Server: nginx
Date: Sun, 03 Aug 2025 09:57:39 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
serial: gateway_a3c823e5b7534299b66a8396169c44f2
serial: gateway_a3c823e5b7534299b66a8396169c44f2
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Pragma: no-cache
Expires: 0

ff
{"code":"SUCCESS","data":{"dataInfo":{"fid":"41,01306e4c39d92a34","fileName":"f087b80bd78541ec85b8cdb51b6aff66.xml","size":13931,"fileUrl":"**********:9384/41,01306e4c39d92a34"}},"serial":"gateway_a3c823e5b7534299b66a8396169c44f2","sucMsg":"操作成功"}
0


0.010725s
POST /emr/index HTTP/1.1
ek-b-clientversion: 1.3.6.399
ek-b-clientip: ***********
ek-b-logintype: 
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jA_u9aw-SmIDxfZ6dh4v9Xpzz7Bl7DQFeiBZ0RYWXBp3QGcDJYAyWpPw4uUQxaLlB4jWPJa_ZNK5sjLCvw8_O_1l3f6-z7Pn3AkyonEy-EC7X3W9w4ZUl4xiPhit53XpLMaSP8GvKwlUpE9-z0sdgeIsdsnXzy-saOLX-uNJj8s
Content-Type: application/json; charset=utf-8
Host: **********:4431
Content-Length: 760
Expect: 100-continue


0.000757s
HTTP/1.1 100 Continue


0.001161s
{"patientId":1148948433428545536,"visitId":1148948433462099968,"patientNo":"574690","visitTime":"1","visitType":"3","title":"08-03 17:48 主治医师查房记录","templateId":924720799015763968,"editMode":0,"parentDocId":1148966573852065792,"fileId":"41,01306e4c39d92a34","startPageNumber":0,"modifyTimeStamp":0,"sortNo":0,"isDel":0,"version":0,"locked":0,"printNum":0,"lockStatusNotAllowEdit":false,"qcFlag":0,"saveFlag":0,"vsValue":{"fC0000001145":"2025-08-03 17:48:43","fC0000009718":null,"fC0000001159":null,"fC0000001147":null,"fC0000001144":"这里由ai编写\r\n/"},"printHeight":0,"printLine":0,"printPage":0,"vsSpecType":[],"showTime":"2025年08月03日 17时48分","signList":[],"menstrualHistory":{},"createDeptCode":"200401","createBedLabel":"28"}


0.163472s
HTTP/1.1 200 
Server: nginx
Date: Sun, 03 Aug 2025 09:57:39 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
serial: gateway_76558e5dfbca4d08b382c04d0ebf2f0a
serial: gateway_76558e5dfbca4d08b382c04d0ebf2f0a
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Pragma: no-cache
Expires: 0

47d
{"code":"SUCCESS","data":{"dataInfo":{"courseType":"2","patientId":"1148948433428545536","startPageNumber":1,"createDeptCode":"200401","typeName":"上级医师查房记录","templateId":"924720799015763968","title":"08-03 17:48 主治医师查房记录","createDT":"2025-08-03 17:57:39","etimeStamp":"1754215059000000000","reviewProcCode":"5","visitId":"1148948433462099968","fileStatus":"0","hospitalId":"425270379058036736","indexId":"1149443113790472192","wardCode":"H2004","applyModifyFlag":"0","locked":0,"processTimeStamp":1754215059595,"authorCode":"5747","patientNo":"574690","author":"韩廷灿","showTime":"2025年08月03日 17时48分","authorId":"1066434522762969088","modifyTimeStamp":1754215059579,"parentDocId":"1148966573852065792","version":0,"typeCode":"**********","visitType":"3","sortNo":1,"indexType":1,"visitTime":1,"createTime":"2025-08-03 17:57:39","editMode":0,"memSyncTimeStamp":1,"stdDocCode":"001013003","auditStatus":"0","bedLabel":"28","typeId":"597451860764262400","isDel":0,"deptCode":"200401","fileId":"41,01306e4c39d92a34","status":"0"}},"serial":"gateway_76558e5dfbca4d08b382c04d0ebf2f0a","sucMsg":"操作成功"}
0


0.006428s
GET /emr/index/auth?id=1149443113790472192&empNo=5747 HTTP/1.1
ek-b-clientversion: 1.3.6.399
ek-b-clientip: ***********
ek-b-logintype: 
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jA_u9aw-SmIDxfZ6dh4v9Xpzz7Bl7DQFeiBZ0RYWXBp3QGcDJYAyWpPw4uUQxaLlB4jWPJa_ZNK5sjLCvw8_O_1l3f6-z7Pn3AkyonEy-EC7X3W9w4ZUl4xiPhit53XpLMaSP8GvKwlUpE9-z0sdgeIsdsnXzy-saOLX-uNJj8s
Host: **********:4431


0.861081s
HTTP/1.1 200 
Server: nginx
Date: Sun, 03 Aug 2025 09:57:40 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
serial: gateway_867b73b9657d400aa48fd0b494f9f38d
serial: gateway_867b73b9657d400aa48fd0b494f9f38d
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Pragma: no-cache
Expires: 0

170
{"code":"SUCCESS","data":{"dataInfo":{"indexToDelete":true,"indexToPrint":false,"indexToDrop":false,"indexToFirstPageModifyApply":false,"indexToCancelAudit":false,"indexToCommit":false,"indexToDelDrop":true,"indexToReject":false,"indexToLock":false,"indexToRefresh":true,"indexToSave":true}},"serial":"gateway_867b73b9657d400aa48fd0b494f9f38d","sucMsg":"操作成功"}
0


0.013658s
POST /emr/CDSS/rj HTTP/1.1
ek-b-clientversion: 1.3.6.399
ek-b-clientip: ***********
ek-b-logintype: 
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jA_u9aw-SmIDxfZ6dh4v9Xpzz7Bl7DQFeiBZ0RYWXBp3QGcDJYAyWpPw4uUQxaLlB4jWPJa_ZNK5sjLCvw8_O_1l3f6-z7Pn3AkyonEy-EC7X3W9w4ZUl4xiPhit53XpLMaSP8GvKwlUpE9-z0sdgeIsdsnXzy-saOLX-uNJj8s
Content-Type: application/json; charset=utf-8
Host: **********:4431
Content-Length: 505
Expect: 100-continue


0.000939s
HTTP/1.1 100 Continue


0.000417s
{"empNos":{"empNo":"5747"},"empRemarks":{"empRemark":"24121605"},"visitId":"574690_1","inHosId":"574690_1","inHosNo":"574690","inHosNum":"1","patName":"李杏香","gender":"F","birthday":"1964-09-09 00:00:00","document":{"docType":"上级医师查房记录","paragraphList":[{"title":"查房日期时间","content":"2025-08-03 17:48:43"},{"title":"查房医师","content":"-"},{"title":"主治医师签名","content":"-"},{"title":"记录人签名","content":"-"},{"title":"查房记录","content":"-"}]}}
0.015388s
HTTP/1.1 200 
Server: nginx
Date: Sun, 03 Aug 2025 09:57:40 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
serial: gateway_0b9d707c1cb04a36af3ddfff2c93f4ae
serial: gateway_0b9d707c1cb04a36af3ddfff2c93f4ae
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Pragma: no-cache
Expires: 0

68
{"code":"SUCCESS","data":{},"serial":"gateway_0b9d707c1cb04a36af3ddfff2c93f4ae","sucMsg":"操作成功"}
0

