GET /oss/file/show?fid=37%2C0125cb61536a10cf HTTP/1.1
ek-b-clientversion: 1.3.6.364
ek-b-clientip: ***********
ek-b-logintype: 
Authorization: Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Host: **********:4431


HTTP/1.1 200 
Server: nginx
Date: Thu, 31 Jul 2025 05:31:16 GMT
Content-Type: text/xml;charset=utf-8
Transfer-Encoding: chunked
Connection: keep-alive
serial: gateway_133187acbe904d1693b913d2d2a0fb27
Last-Modified: 1752069766000
Content-Disposition: inline; filename="8a6a8a14047d4503953af3c8c7bf4ff0.xml"
serial: gateway_133187acbe904d1693b913d2d2a0fb27
Accept-Ranges: bytes
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Pragma: no-cache
Expires: 0

1c8c0
﻿<?xml version="1.0" encoding="utf-8"?>
<XTextDocument xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" EditorVersionString="DCWriter20250219084657">
   <Attributes>
      <Attribute>
         <Name>科室</Name>
         <Value>神经内科总院</Value>
      </Attribute>
      <Attribute>
         <Name>科室Code</Name>
         <Value>200401</Value>
      </Attribute>
      <Attribute>
         <Name>病区</Name>
         <Value>神经内科总院病区护士站</Value>
      </Attribute>
      <Attribute>
         <Name>床号</Name>
         <Value>12</Value>
      </Attribute>
   </Attributes>
   <InnerID>2365</InnerID>
   <ContentReadonly>True</ContentReadonly>
   <XElements>
      <Element xsi:type="XTextHeader">
         <InnerID>6</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="XInputField" StyleIndex="8">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>医院名称</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000015114</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>614</InnerID>
               <ID>field42</ID>
               <ToolTip>【医院名称】</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000210</DataSource>
                  <BindingPath>CI00002253</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="8">
                     <InnerID>1221</InnerID>
                     <Text>鹤壁市人民医院</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <Name>FC0000015114</Name>
               <InnerValue>鹤壁市人民医院</InnerValue>
               <BackgroundText>医院名称</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag" StyleIndex="8">
               <InnerID>9</InnerID>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="7">
               <InnerID>10</InnerID>
               <ID>label15</ID>
               <Deleteable>false</Deleteable>
               <Width>413.7817</Width>
               <Height>85.54686</Height>
               <Text>病 程 记 录</Text>
            </Element>
            <Element xsi:type="XParagraphFlag" StyleIndex="6">
               <InnerID>11</InnerID>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <InnerID>12</InnerID>
               <ID>label16</ID>
               <Deleteable>false</Deleteable>
               <Width>131.25</Width>
               <Height>49.90234</Height>
               <Text>姓名：</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="5">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>患者姓名</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001099</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>13</InnerID>
               <ID>field12</ID>
               <ToolTip>【患者姓名】患者本人在公安户籍管理部门正式登记注册的姓氏和名称</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000206</DataSource>
                  <BindingPath>CI00002200</BindingPath>
               </ValueBinding>
               <ContentReadonly>True</ContentReadonly>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="5">
                     <InnerID>1222</InnerID>
                     <Text>程太生</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <SpecifyWidth>-236.2205</SpecifyWidth>
               <UserEditable>false</UserEditable>
               <Name>FC0000001099</Name>
               <InnerValue>程太生</InnerValue>
               <BackgroundText>患者姓名</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString" WhitespaceCount="1">
               <InnerID>1217</InnerID>
               <Text> </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <InnerID>15</InnerID>
               <ID>label17</ID>
               <Deleteable>false</Deleteable>
               <Width>131.25</Width>
               <Height>49.90234</Height>
               <Text>科室：</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="5">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>科室名称</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001104</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>598</InnerID>
               <ID>field43</ID>
               <ToolTip>【科室名称】患者在医疗机构就诊的科室名称</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000206</DataSource>
                  <BindingPath>CI00002240</BindingPath>
               </ValueBinding>
               <ContentReadonly>True</ContentReadonly>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="5">
                     <InnerID>1223</InnerID>
                     <Text>神经内科总院</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <Name>FC0000001104</Name>
               <InnerValue>神经内科总院</InnerValue>
               <BackgroundText>科室名称</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString" WhitespaceCount="2">
               <InnerID>1218</InnerID>
               <Text>  </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <InnerID>21</InnerID>
               <ID>label19</ID>
               <Deleteable>false</Deleteable>
               <Width>131.25</Width>
               <Height>49.90234</Height>
               <Text>床号：</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="5">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>病床号</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001092</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>624</InnerID>
               <ID>field47</ID>
               <ToolTip>【病床号】患者住院期间，所住床位对应的编号</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000206</DataSource>
                  <BindingPath>CI00002206</BindingPath>
               </ValueBinding>
               <ContentReadonly>True</ContentReadonly>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="5">
                     <InnerID>1224</InnerID>
                     <Text>12</Text>
                  </Element>
               </XElements>
               <BorderElementColor />
               <Name>FC0000001092</Name>
               <InnerValue>12</InnerValue>
               <BackgroundText>病床号</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString" WhitespaceCount="2">
               <InnerID>1219</InnerID>
               <Text>  </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <InnerID>24</InnerID>
               <ID>label20</ID>
               <Deleteable>false</Deleteable>
               <Width>175</Width>
               <Height>49.90234</Height>
               <Text>住院号：</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="5">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>住院号</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001114</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>25</InnerID>
               <ID>field16</ID>
               <ToolTip>【住院号】按照某一特定编码规则赋予住院就诊对象的顺序号</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000206</DataSource>
                  <BindingPath>CI00002198</BindingPath>
               </ValueBinding>
               <ContentReadonly>True</ContentReadonly>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="5">
                     <InnerID>1225</InnerID>
                     <Text>572359</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <SpecifyWidth>-188.9764</SpecifyWidth>
               <UserEditable>false</UserEditable>
               <Name>FC0000001114</Name>
               <InnerValue>572359</InnerValue>
               <BackgroundText>住院号</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString" StyleIndex="1">
               <InnerID>1220</InnerID>
               <Text> 住院次数:</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="1">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>住院次数</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000015208</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>N</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>617</InnerID>
               <ID>field46</ID>
               <ToolTip>【住院次数】</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000206</DataSource>
                  <BindingPath>CI00002199</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="1">
                     <InnerID>1226</InnerID>
                     <Text>1</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <Name>FC0000015208</Name>
               <InnerValue>1</InnerValue>
               <BackgroundText>住院次数</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag" StyleIndex="1">
               <InnerID>26</InnerID>
            </Element>
         </XElements>
      </Element>
      <Element xsi:type="XTextBody">
         <InnerID>27</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="XInputField" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>记录日期时间</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001100</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>DT</Value>
                  </Attribute>
                  <Attribute>
                     <Name>ShowTimeFlag</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>28</InnerID>
               <ID>field18</ID>
               <ToolTip>【记录日期时间】完成此项业务活动时的公元纪年日期和时间的完整描述</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000210</DataSource>
                  <BindingPath>CI00002256</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="3">
                     <InnerID>1252</InnerID>
                     <Text>2025年07月09日 22时01分</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <Name>FC0000001100</Name>
               <DisplayFormat>
                  <Style>DateTime</Style>
                  <Format>yyyy年MM月dd日 HH时mm分</Format>
               </DisplayFormat>
               <InnerValue>2025/7/9 22:01:55</InnerValue>
               <BackgroundText>yyyy年MM月dd日 HH时mm分</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings>
                  <EditStyle>DateTime</EditStyle>
               </FieldSettings>
            </Element>
            <Element xsi:type="XString" WhitespaceCount="15">
               <InnerID>1227</InnerID>
               <Text>               </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="9">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>内容</Value>
                  </Attribute>
               </Attributes>
               <InnerID>30</InnerID>
               <ID>FC0000009697</ID>
               <Deleteable>false</Deleteable>
               <Name>0</Name>
               <Width>263.1408</Width>
               <Height>49.90234</Height>
               <Text>首次病程记录</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>31</InnerID>
            </Element>
            <Element xsi:type="XString">
               <InnerID>1228</InnerID>
               <Text>    姓名：</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="5">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>患者姓名</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001099</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>33</InnerID>
               <ID>field17</ID>
               <ToolTip>【患者姓名】患者本人在公安户籍管理部门正式登记注册的姓氏和名称</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000206</DataSource>
                  <BindingPath>CI00002200</BindingPath>
               </ValueBinding>
               <ContentReadonly>True</ContentReadonly>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="5">
                     <InnerID>1253</InnerID>
                     <Text>程太生</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <UserEditable>false</UserEditable>
               <Name>FC0000001099</Name>
               <InnerValue>程太生</InnerValue>
               <BackgroundText>患者姓名</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString">
               <InnerID>1229</InnerID>
               <Text>，性别：</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="5">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>性别代码</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001108</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S3</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>35</InnerID>
               <ID>field10</ID>
               <ToolTip>【性别代码】患者生理性别在特定编码体系中的代码</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000206</DataSource>
                  <BindingPath>CI00002201</BindingPath>
               </ValueBinding>
               <ContentReadonly>True</ContentReadonly>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="5">
                     <InnerID>1254</InnerID>
                     <Text>男</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <UserEditable>false</UserEditable>
               <Name>FC0000001108</Name>
               <InnerValue>男</InnerValue>
               <BackgroundText>性别代码</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <EnableLastSelectedListItems>true</EnableLastSelectedListItems>
               <FieldSettings>
                  <EditStyle>DropdownList</EditStyle>
               </FieldSettings>
            </Element>
            <Element xsi:type="XString">
               <InnerID>1230</InnerID>
               <Text>，年龄：</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="5">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>年龄(岁)</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001105</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>N</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>37</InnerID>
               <ID>field10637638539604323332</ID>
               <ToolTip>【年龄(岁)】患者年龄满1周岁的实足年龄，为患者出生后按照日历计算的历法年龄，以实足年龄的相应整数填写</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000206</DataSource>
                  <BindingPath>CI00002202</BindingPath>
               </ValueBinding>
               <ContentReadonly>True</ContentReadonly>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="5">
                     <InnerID>1255</InnerID>
                     <Text>75</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <UserEditable>false</UserEditable>
               <Name>FC0000001105</Name>
               <InnerValue>75</InnerValue>
               <BackgroundText>XXX</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XInputField">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>年龄单位</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000015219</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>566</InnerID>
               <ID>field48</ID>
               <ToolTip>【年龄单位】</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000206</DataSource>
                  <BindingPath>CI00002221</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1256</InnerID>
                     <Text>岁</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <UserEditable>false</UserEditable>
               <Name>FC0000015219</Name>
               <InnerValue>岁</InnerValue>
               <BackgroundText>X</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString">
               <InnerID>1231</InnerID>
               <Text>，因“</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>主诉</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001113</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>39</InnerID>
               <ID>field10637638539604323333</ID>
               <ToolTip>【主诉】对患者本次疾病相关的主要症状及其持续时间的描述，一般由患者本人或监护人描述</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000212</DataSource>
                  <BindingPath>CI00002263</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="3">
                     <InnerID>1257</InnerID>
                     <Text>认知功能下降半年，加重1周</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <Name>FC0000001113</Name>
               <InnerValue>认知功能下降半年，加重1周</InnerValue>
               <BackgroundText>主诉在此次录入！！！</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString">
               <InnerID>1232</InnerID>
               <Text>”入院。</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>41</InnerID>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>病例特点</Value>
                  </Attribute>
               </Attributes>
               <InnerID>42</InnerID>
               <ID>FC0000001094</ID>
               <Deleteable>false</Deleteable>
               <Name>0.1</Name>
               <Width>306.25</Width>
               <Height>49.90234</Height>
               <Text>一、病例特点：</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>1522</InnerID>
            </Element>
            <Element xsi:type="XString" WhitespaceCount="4">
               <InnerID>1233</InnerID>
               <Text>    </Text>
            </Element>
            <Element xsi:type="XTextLabelElement">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>患者特点</Value>
                  </Attribute>
               </Attributes>
               <InnerID>1530</InnerID>
               <ID>FC0000001023</ID>
               <Deleteable>false</Deleteable>
               <Name>0.1.10</Name>
               <Width>262.5</Width>
               <Height>49.90234</Height>
               <Text>1.患者特点：</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>43</InnerID>
            </Element>
            <Element xsi:type="XString" StyleIndex="1" WhitespaceCount="4">
               <InnerID>1234</InnerID>
               <Text>    </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>现病史</Value>
                  </Attribute>
               </Attributes>
               <InnerID>45</InnerID>
               <ID>FC0000008649</ID>
               <Deleteable>false</Deleteable>
               <Name>0.1.1</Name>
               <Width>218.75</Width>
               <Height>49.90234</Height>
               <Text>2.现病史：</Text>
            </Element>
            <Element xsi:type="XInputField">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>现病史内容</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000000850</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>181</InnerID>
               <ID>field44</ID>
               <ToolTip>【现病史内容】</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000212</DataSource>
                  <BindingPath>CI00002264</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1258</InnerID>
                     <Text>近半年来记忆力、理解力下降，近1周来明显加重，做饭糊锅、不知道放调料，忘记怎么使用空调电视遥控器，忘记早餐吃过什么，经门诊以“认知障碍” 收住入院。发病以来，精神状态尚可，食欲正常，睡眠正常，大便正常，小便正常，体重无变化</Text>
                  </Element>
               </XElements>
               <BorderElementColor />
               <Name>FC0000000850</Name>
               <InnerValue>近半年来记忆力、理解力下降，近1周来明显加重，做饭糊锅、不知道放调料，忘记怎么使用空调电视遥控器，忘记早餐吃过什么，经门诊以“认知障碍” 收住入院。发病以来，精神状态尚可，食欲正常，睡眠正常，大便正常，小便正常，体重无变化</InnerValue>
               <BackgroundText>现病史内容</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>46</InnerID>
            </Element>
            <Element xsi:type="XString" StyleIndex="1" WhitespaceCount="4">
               <InnerID>1235</InnerID>
               <Text>    </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>既往史</Value>
                  </Attribute>
               </Attributes>
               <InnerID>48</InnerID>
               <ID>FC0000008650</ID>
               <Deleteable>false</Deleteable>
               <Name>0.1.2</Name>
               <Width>218.75</Width>
               <Height>49.90234</Height>
               <Text>3.既往史：</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <InnerID>63</InnerID>
               <ID>field37</ID>
               <PropertyExpressions>
                  <Item Name="Visible">[field22]='1'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <VisibleExpression>[field22]='1'</VisibleExpression>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1259</InnerID>
                     <Text>，</Text>
                  </Element>
                  <Element xsi:type="XInputField" StyleIndex="3">
                     <Attributes>
                        <Attribute>
                           <Name>FromType</Name>
                           <Value>emrfield</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldName</Name>
                           <Value>传染病史</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldCode</Name>
                           <Value>FC0000010248</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldDataType</Name>
                           <Value>S1</Value>
                        </Attribute>
                        <Attribute>
                           <Name>IsSignInput</Name>
                           <Value>0</Value>
                        </Attribute>
                        <Attribute>
                           <Name>SignShowType</Name>
                           <Value>0</Value>
                        </Attribute>
                     </Attributes>
                     <InnerID>65</InnerID>
                     <ID>field23</ID>
                     <ToolTip>【传染病史】患者既往所患各种急性或慢性传染性疾病名称的详细描述</ToolTip>
                     <EnableValueValidate>true</EnableValueValidate>
                     <ValueBinding />
                     <Deleteable>false</Deleteable>
                     <BorderElementColor />
                     <Name>FC0000010248</Name>
                     <BackgroundText>请在此输入传染病史！！！</BackgroundText>
                     <EditorActiveMode>MouseClick</EditorActiveMode>
                     <FieldSettings />
                  </Element>
               </XElements>
               <Visible>false</Visible>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <InnerValue>，</InnerValue>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <InnerID>70</InnerID>
               <ID>field38</ID>
               <PropertyExpressions>
                  <Item Name="Visible">[field24]='1'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <VisibleExpression>[field24]='1'</VisibleExpression>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1260</InnerID>
                     <Text>：</Text>
                  </Element>
                  <Element xsi:type="XInputField" StyleIndex="3">
                     <Attributes>
                        <Attribute>
                           <Name>FromType</Name>
                           <Value>emrfield</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldName</Name>
                           <Value>手术史</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldCode</Name>
                           <Value>FC0000010250</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldDataType</Name>
                           <Value>S1</Value>
                        </Attribute>
                        <Attribute>
                           <Name>IsSignInput</Name>
                           <Value>0</Value>
                        </Attribute>
                        <Attribute>
                           <Name>SignShowType</Name>
                           <Value>0</Value>
                        </Attribute>
                     </Attributes>
                     <InnerID>72</InnerID>
                     <ID>field25</ID>
                     <ToolTip>【手术史】患者既往接受手术/操作经历的详细描述</ToolTip>
                     <EnableValueValidate>true</EnableValueValidate>
                     <ValueBinding />
                     <Deleteable>false</Deleteable>
                     <BorderElementColor />
                     <Name>FC0000010250</Name>
                     <BackgroundText>请在此输入手术史！！！</BackgroundText>
                     <EditorActiveMode>MouseClick</EditorActiveMode>
                     <FieldSettings />
                  </Element>
               </XElements>
               <Visible>false</Visible>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <InnerValue>：</InnerValue>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <InnerID>77</InnerID>
               <ID>field39</ID>
               <PropertyExpressions>
                  <Item Name="Visible">[field26]='1'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <VisibleExpression>[field26]='1'</VisibleExpression>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1261</InnerID>
                     <Text>：</Text>
                  </Element>
                  <Element xsi:type="XInputField" StyleIndex="3">
                     <Attributes>
                        <Attribute>
                           <Name>FromType</Name>
                           <Value>emrfield</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldName</Name>
                           <Value>疾病史(含外伤)</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldCode</Name>
                           <Value>FC0000010252</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldDataType</Name>
                           <Value>S1</Value>
                        </Attribute>
                        <Attribute>
                           <Name>IsSignInput</Name>
                           <Value>0</Value>
                        </Attribute>
                        <Attribute>
                           <Name>SignShowType</Name>
                           <Value>0</Value>
                        </Attribute>
                     </Attributes>
                     <InnerID>79</InnerID>
                     <ID>field27</ID>
                     <ToolTip>【疾病史(含外伤)】患者既往健康状况和疾病（含外伤）的详细描述</ToolTip>
                     <EnableValueValidate>true</EnableValueValidate>
                     <ValueBinding />
                     <Deleteable>false</Deleteable>
                     <BorderElementColor />
                     <Name>FC0000010252</Name>
                     <BackgroundText>请在此输入外伤史！！！</BackgroundText>
                     <EditorActiveMode>MouseClick</EditorActiveMode>
                     <FieldSettings />
                  </Element>
               </XElements>
               <Visible>false</Visible>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <InnerValue>：</InnerValue>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <InnerID>84</InnerID>
               <ID>field40</ID>
               <PropertyExpressions>
                  <Item Name="Visible">[field28]='1'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <VisibleExpression>[field28]='1'</VisibleExpression>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1262</InnerID>
                     <Text>：</Text>
                  </Element>
                  <Element xsi:type="XInputField" StyleIndex="3">
                     <Attributes>
                        <Attribute>
                           <Name>FromType</Name>
                           <Value>emrfield</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldName</Name>
                           <Value>输血史</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldCode</Name>
                           <Value>FC0000010253</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldDataType</Name>
                           <Value>S1</Value>
                        </Attribute>
                        <Attribute>
                           <Name>IsSignInput</Name>
                           <Value>0</Value>
                        </Attribute>
                        <Attribute>
                           <Name>SignShowType</Name>
                           <Value>0</Value>
                        </Attribute>
                     </Attributes>
                     <InnerID>86</InnerID>
                     <ID>field29</ID>
                     <ToolTip>【输血史】患者既往输血史的详细描述</ToolTip>
                     <EnableValueValidate>true</EnableValueValidate>
                     <ValueBinding />
                     <Deleteable>false</Deleteable>
                     <BorderElementColor />
                     <Name>FC0000010253</Name>
                     <BackgroundText>请在此输入输血史！！！</BackgroundText>
                     <EditorActiveMode>MouseClick</EditorActiveMode>
                     <FieldSettings />
                  </Element>
               </XElements>
               <Visible>false</Visible>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <InnerValue>：</InnerValue>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <InnerID>91</InnerID>
               <ID>field41</ID>
               <PropertyExpressions>
                  <Item Name="Visible">[field30]='1'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <VisibleExpression>[field30]='1'</VisibleExpression>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1263</InnerID>
                     <Text>：</Text>
                  </Element>
                  <Element xsi:type="XInputField" StyleIndex="3">
                     <Attributes>
                        <Attribute>
                           <Name>FromType</Name>
                           <Value>emrfield</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldName</Name>
                           <Value>过敏史</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldCode</Name>
                           <Value>FC0000010255</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldDataType</Name>
                           <Value>S1</Value>
                        </Attribute>
                        <Attribute>
                           <Name>IsSignInput</Name>
                           <Value>0</Value>
                        </Attribute>
                        <Attribute>
                           <Name>SignShowType</Name>
                           <Value>0</Value>
                        </Attribute>
                     </Attributes>
                     <InnerID>93</InnerID>
                     <ID>field31</ID>
                     <ToolTip>【过敏史】患者既往发生过敏情况的详细描述</ToolTip>
                     <EnableValueValidate>true</EnableValueValidate>
                     <ValueBinding />
                     <Deleteable>false</Deleteable>
                     <BorderElementColor />
                     <Name>FC0000010255</Name>
                     <BackgroundText>请在此输入过敏史！！！</BackgroundText>
                     <EditorActiveMode>MouseClick</EditorActiveMode>
                     <FieldSettings />
                  </Element>
               </XElements>
               <Visible>false</Visible>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <InnerValue>：</InnerValue>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="4">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>   防接种史</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000010257</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>97</InnerID>
               <ID>field33</ID>
               <ToolTip>【预防接种史】患者预防接种情况的详细描述</ToolTip>
               <PropertyExpressions>
                  <Item Name="Visible">[field32]='0'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <VisibleExpression>[field32]='0'</VisibleExpression>
               <Visible>false</Visible>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <Name>FC0000010257</Name>
               <BackgroundText>请在此输入预防接种史！！！</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XInputField">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>既往史内容</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000000854</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>804</InnerID>
               <ID>field26</ID>
               <ToolTip>【既往史内容】</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000212</DataSource>
                  <BindingPath>CI00002265</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1264</InnerID>
                     <Text>既往身体健康，否认高血压,糖尿病,冠心病-心肌梗死,脑梗死,高胆固醇血症等疾病史。无甲状腺功能异常，否认病毒性肝炎、肺结核等传染病史；无手术史；无外伤史；无输血史；无过敏史；预防接种史不详。</Text>
                  </Element>
               </XElements>
               <BorderElementColor />
               <Name>FC0000000854</Name>
               <InnerValue>既往身体健康，否认高血压,糖尿病,冠心病-心肌梗死,脑梗死,高胆固醇血症等疾病史。无甲状腺功能异常，否认病毒性肝炎、肺结核等传染病史；无手术史；无外伤史；无输血史；无过敏史；预防接种史不详。</InnerValue>
               <BackgroundText>既往史内容</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>99</InnerID>
            </Element>
            <Element xsi:type="XString" WhitespaceCount="4">
               <InnerID>1236</InnerID>
               <Text>    </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>体格检查</Value>
                  </Attribute>
               </Attributes>
               <InnerID>101</InnerID>
               <ID>FC0000008651</ID>
               <Deleteable>false</Deleteable>
               <Name>0.1.3</Name>
               <Width>262.5</Width>
               <Height>49.90234</Height>
               <Text>4.体格检查：</Text>
            </Element>
            <Element xsi:type="XInputField">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>体格检查内容</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000000855</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>818</InnerID>
               <ID>field28</ID>
               <ToolTip>【体格检查内容】</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000212</DataSource>
                  <BindingPath>CI00002286</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1265</InnerID>
                     <Text>T:36℃ P:72次/分 R:18次/分 BP:140/83mmHg,发育正常,色泽正常,胸廓对称,无畸形,双肺呼吸音清晰,未闻及干、湿性啰音,未闻及胸膜摩擦音,无哮鸣音,心脏相对浊音界无扩大,心率72次/分,心律齐心音正常,各瓣膜听诊区未闻及病理性杂音,腹部平坦,腹壁无静脉曲张,腹部平坦,无胃肠蠕动波,腹壁柔软,腹部无压痛,无反跳痛,墨菲氏征阴性,双肾无叩击痛,生理反射存在,病理反射阴性,脑膜刺激征阴性。</Text>
                  </Element>
               </XElements>
               <BorderElementColor />
               <Name>FC0000000855</Name>
               <InnerValue>T:36℃ P:72次/分 R:18次/分 BP:140/83mmHg,发育正常,色泽正常,胸廓对称,无畸形,双肺呼吸音清晰,未闻及干、湿性啰音,未闻及胸膜摩擦音,无哮鸣音,心脏相对浊音界无扩大,心率72次/分,心律齐心音正常,各瓣膜听诊区未闻及病理性杂音,腹部平坦,腹壁无静脉曲张,腹部平坦,无胃肠蠕动波,腹壁柔软,腹部无压痛,无反跳痛,墨菲氏征阴性,双肾无叩击痛,生理反射存在,病理反射阴性,脑膜刺激征阴性。</InnerValue>
               <BackgroundText>体格检查内容</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>1539</InnerID>
            </Element>
            <Element xsi:type="XString" StyleIndex="1" WhitespaceCount="6">
               <InnerID>1237</InnerID>
               <Text>      </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="1">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>专科情况</Value>
                  </Attribute>
               </Attributes>
               <InnerID>2749</InnerID>
               <ID>FC0000001022</ID>
               <Name>0.21</Name>
               <Width>218.75</Width>
               <Height>49.90234</Height>
               <Text>专科情况：</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="1">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>datasourse</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>专科情况</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>CI00002267</Value>
                  </Attribute>
               </Attributes>
               <InnerID>616</InnerID>
               <ID>field50</ID>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000212</DataSource>
                  <BindingPath>CI00002267</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="1">
                     <InnerID>1266</InnerID>
                     <Text>意识清醒，语言清晰，瞬时记忆力正常，短时记忆力正常，长时记忆力正常，理解力正常，计算力正常，判断力正常，定向力正常，右利手。颅神经：        嗅神经：左正常，右正常。    视神经：近视力：左正常，右正常；远视力：左正常，右正常；视野（粗测）无缺损。    动眼神经、滑车神经、外展神经：睑下垂：左无，右无；眼球形态正常，位置居中，复视无。瞳孔直径：左3.0mm,右3.0mm,形态左正常，右正常。眼球震颤：无，眼球运动：正常。    三叉神经：感觉：左正常，右正常；角膜反射：直接：左正常，右正常；间接：左正常，右正常。运动：下颌偏向：无；嚼肌正常，颞肌正常；下颌反射：正常。    面神经：眼裂：左正常，右正常。鼻唇沟：左正常，右正常。闭目：左正常，右正常；皱额：左正常，右正常；鼓气、露齿、吹口哨：正常。    位听神经：耳语：左正常，右正常。    舌咽神经、迷走神经：发音正常，味觉感觉正常；咽反射：左正常，右正常。软腭动度：左正常，右正常；悬雍垂：居中。    副神经：胸锁乳突肌、斜方肌：左正常，右正常。    舌下神经：伸舌偏向：正常；萎缩：左无，右无；舌肌纤颤：左无，右无。运动系统    自行走入院入院，步态正常，肌肉萎缩：无，肌束颤动：无。    肌力：左：肩关节5级；肘关节5级；腕关节5级；指关节5级；          右：肩关节5级；肘关节5级；腕关节5级；指关节5级             左：髋关节5级；膝关节5级；踝关节5级；趾关节5级；          右：髋关节5级；膝关节5级；踝关节5级；趾关节5级；    肌张力：正常。    不自主运动：无。    共济运动：指鼻试验：左正常，右正常；快复动作：左正常，右正常；快速轮替动作：左正常，右正常；肌反击现象：左正常，右正常；跟膝胫试验：左正常，右正常。闭目难立征：阴性。感觉系统    痛温觉障碍部位无，程度无，触觉障碍部位无，程度无。    位置觉：正常；震动觉：正常；运动觉：正常。皮肤定位觉：正常；两点辨别觉：正常；实体觉：正常；体表图形觉：正常。    神经反射（消失- 降低+ 正常++ 活跃+++ 亢进++++）左右左右腹壁反射上（T7-8）++++肱三头肌反射（C6-7）++++       中（T9-10）++++桡骨膜反射（C5-8）++++       下（T11-12）++++膝反射（L2-4）++++肱二头肌反射（C5-6）++++跟腱反射（S1-2）++++Babinski征--Gordon征--Oppenheim征--Hoffmann征--    脑膜刺激征：颈强直：阴性。kernig征：阴性；Brudzinski征：阴性，    神经干牵拉试验：Lasegue征：左侧阴性，右侧阴性。    其他：无。 临床评估表            VTE风险评估：2分</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <Name>CI00002267</Name>
               <InnerValue>意识清醒，语言清晰，瞬时记忆力正常，短时记忆力正常，长时记忆力正常，理解力正常，计算力正常，判断力正常，定向力正常，右利手。颅神经：        嗅神经：左正常，右正常。    视神经：近视力：左正常，右正常；远视力：左正常，右正常；视野（粗测）无缺损。    动眼神经、滑车神经、外展神经：睑下垂：左无，右无；眼球形态正常，位置居中，复视无。瞳孔直径：左3.0mm,右3.0mm,形态左正常，右正常。眼球震颤：无，眼球运动：正常。    三叉神经：感觉：左正常，右正常；角膜反射：直接：左正常，右正常；间接：左正常，右正常。运动：下颌偏向：无；嚼肌正常，颞肌正常；下颌反射：正常。    面神经：眼裂：左正常，右正常。鼻唇沟：左正常，右正常。闭目：左正常，右正常；皱额：左正常，右正常；鼓气、露齿、吹口哨：正常。    位听神经：耳语：左正常，右正常。    舌咽神经、迷走神经：发音正常，味觉感觉正常；咽反射：左正常，右正常。软腭动度：左正常，右正常；悬雍垂：居中。    副神经：胸锁乳突肌、斜方肌：左正常，右正常。    舌下神经：伸舌偏向：正常；萎缩：左无，右无；舌肌纤颤：左无，右无   运动系统    自行走入院入院，步态正常，肌肉萎缩：无，肌束颤动：无。    肌力：左：肩关节5级；肘关节5级；腕关节5级；指关节5级；          右：肩关节5级；肘关节5级；腕关节5级；指关节5级；          左：髋关节5级；膝关节5级；踝关节5级；趾关节5级；          右：髋关节5级；膝关节5级；踝关节5级；趾关节5级；    肌张力：正常。    不自主运动：无。    共济运动：指鼻试验：左正常，右正常；快复动作：左正常，右正常；快速轮替动作：左正常，右正常；肌反击现象：左正常，右正常；跟膝胫试验：左正常，右正常。闭目难立征：阴性。感觉系统    痛温觉障碍部位无，程度无，触觉障碍部位无，程度无。    位置觉：正常；震动觉：正常；运动觉：正常。皮肤定位觉：正常；两点辨别觉：正常；实体觉：正常；体表图形觉：正常。    神经反射（消失- 降低+ 正常++ 活跃+++ 亢进++++）左右左右腹壁反射上（T7-8）++++肱三头肌反射（C6-7）++++       中（T9-10）++++桡骨膜反射（C5-8）++++       下（T11-12）++++膝反射（L2-4）++++肱二头肌反射（C5-6）++++跟腱反射（S1-2）++++Babinski征--Gordon征--Oppenheim征--Hoffmann征--    脑膜刺激征：颈强直：阴性。kernig征：阴性；Brudzinski征：阴性，    神经干牵拉试验：Lasegue征：左侧阴性，右侧阴性。    其他：无。 临床评估表            VTE风险评估：2分</InnerValue>
               <BackgroundText>专科情况</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>102</InnerID>
            </Element>
            <Element xsi:type="XString" StyleIndex="1" WhitespaceCount="4">
               <InnerID>1238</InnerID>
               <Text>    </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>辅助检查结果</Value>
                  </Attribute>
               </Attributes>
               <InnerID>104</InnerID>
               <ID>FC0000008652</ID>
               <Deleteable>false</Deleteable>
               <Name>0.1.4</Name>
               <Width>262.5</Width>
               <Height>49.90234</Height>
               <Text>5.辅助检查：</Text>
            </Element>
            <Element xsi:type="XInputField">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>辅助检查内容</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000000856</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>827</InnerID>
               <ID>field30</ID>
               <ToolTip>【辅助检查内容】</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000212</DataSource>
                  <BindingPath>CI00002268</BindingPath>
               </ValueBinding>
               <XElements>
                  <Element xsi:type="XInputField">
                     <Attributes>
                        <Attribute>
                           <Name>FromType</Name>
                           <Value>emrfield</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldName</Name>
                           <Value>辅助检查内容</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldCode</Name>
                           <Value>FC0000000856</Value>
                        </Attribute>
                        <Attribute>
                           <Name>FieldDataType</Name>
                           <Value>S1</Value>
                        </Attribute>
                        <Attribute>
                           <Name>IsSignInput</Name>
                           <Value>0</Value>
                        </Attribute>
                        <Attribute>
                           <Name>SignShowType</Name>
                           <Value>0</Value>
                        </Attribute>
                     </Attributes>
                     <InnerID>836</InnerID>
                     <ID>field32</ID>
                     <ToolTip>【辅助检查内容】</ToolTip>
                     <EnableValueValidate>true</EnableValueValidate>
                     <ValueBinding>
                        <DataSource>TI00000212</DataSource>
                        <BindingPath>CI00002268</BindingPath>
                     </ValueBinding>
                     <XElements>
                        <Element xsi:type="XString">
                           <InnerID>1267</InnerID>
                           <Text>暂无</Text>
                        </Element>
                     </XElements>
                     <BorderElementColor />
                     <Name>FC0000000856</Name>
                     <InnerValue>暂无</InnerValue>
                     <BackgroundText>辅助检查内容</BackgroundText>
                     <EditorActiveMode>MouseClick</EditorActiveMode>
                     <FieldSettings />
                  </Element>
               </XElements>
               <BorderElementColor />
               <Name>FC0000000856</Name>
               <InnerValue>暂无</InnerValue>
               <BackgroundText>辅助检查内容</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>105</InnerID>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <InnerID>638</InnerID>
               <ID>label25</ID>
               <Deleteable>false</Deleteable>
               <Name>0.35</Name>
               <Width>43.75</Width>
               <Height>49.90234</Height>
               <Text>二</Text>
            </Element>
            <Element xsi:type="XString" StyleIndex="3">
               <InnerID>1239</InnerID>
               <Text>、</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <InnerID>602</InnerID>
               <ID>诊断类型</ID>
               <EnableValueValidate>true</EnableValueValidate>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1268</InnerID>
                     <Text>入院诊断</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <EventExpressions>
                  <Expression>
                     <Expression>value='初步诊断'</Expression>
                     <Target>Custom</Target>
                     <CustomTargetName>field174</CustomTargetName>
                     <TargetPropertyName>Visible</TargetPropertyName>
                  </Expression>
                  <Expression>
                     <Expression>value='初步诊断'</Expression>
                     <Target>Custom</Target>
                     <CustomTargetName>field161</CustomTargetName>
                     <TargetPropertyName>Visible</TargetPropertyName>
                  </Expression>
                  <Expression>
                     <Expression>value='入院诊断'</Expression>
                     <Target>Custom</Target>
                     <CustomTargetName>field197</CustomTargetName>
                     <TargetPropertyName>Visible</TargetPropertyName>
                  </Expression>
                  <Expression>
                     <Expression>value='入院诊断'</Expression>
                     <Target>Custom</Target>
                     <CustomTargetName>field262</CustomTargetName>
                     <TargetPropertyName>Visible</TargetPropertyName>
                  </Expression>
                  <Expression>
                     <Expression>value='初步诊断'</Expression>
                     <Target>Custom</Target>
                     <CustomTargetName>field263</CustomTargetName>
                     <TargetPropertyName>Visible</TargetPropertyName>
                  </Expression>
                  <Expression>
                     <Expression>value='入院诊断'</Expression>
                     <Target>Custom</Target>
                     <CustomTargetName>field267</CustomTargetName>
                     <TargetPropertyName>Visible</TargetPropertyName>
                  </Expression>
               </EventExpressions>
               <UserEditable>false</UserEditable>
               <Name>cb3d7b3a-875e-4d19-8f59-ea50ae6f7413</Name>
               <InnerValue>入院诊断</InnerValue>
               <BackgroundText>请选择值</BackgroundText>
               <EditorActiveMode>GotFocus MouseClick Enter</EditorActiveMode>
               <SelectedIndex>1</SelectedIndex>
               <FieldSettings>
                  <EditStyle>DropdownList</EditStyle>
                  <ListSource>
                     <Items>
                        <Item>
                           <Text>初步诊断</Text>
                        </Item>
                        <Item>
                           <Text>入院诊断</Text>
                        </Item>
                     </Items>
                  </ListSource>
               </FieldSettings>
            </Element>
            <Element xsi:type="XString" StyleIndex="3">
               <InnerID>1240</InnerID>
               <Text>：</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="5">
               <Attributes>
                  <Attribute>
                     <Name>InputType</Name>
                     <Value>Diag</Value>
                  </Attribute>
                  <Attribute>
                     <Name>DiagType</Name>
                     <Value>入院诊断</Value>
                  </Attribute>
                  <Attribute>
                     <Name>DiagFormat</Name>
                     <Value>{DiagNo}.{FullName}
</Value>
                  </Attribute>
                  <Attribute>
                     <Name>DiagCLB</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>入院诊断西医诊断名称</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000000939</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>345</InnerID>
               <ID>field52</ID>
               <ToolTip>【入院诊断西医诊断名称】由医师根据患者就诊时的情况，综合分析所作出的西医诊断名称</ToolTip>
               <PropertyExpressions>
                  <Item Name="Visible">[诊断类型]='入院诊断'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <VisibleExpression>[诊断类型]='入院诊断'</VisibleExpression>
               <ContentReadonly>True</ContentReadonly>
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1269</InnerID>
                     <Text>1.认知障碍</Text>
                  </Element>
                  <Element xsi:type="XParagraphFlag">
                     <InnerID>2376</InnerID>
                  </Element>
                  <Element xsi:type="XString">
                     <InnerID>1270</InnerID>
                     <Text>2.肌张力障碍</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <UserEditable>false</UserEditable>
               <Name>FC0000000939</Name>
               <InnerValue>1.认知障碍
2.肌张力障碍</InnerValue>
               <BackgroundText>入院诊断西医诊断名称</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString" StyleIndex="3" WhitespaceCount="1">
               <InnerID>1241</InnerID>
               <Text> </Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="5">
               <Attributes>
                  <Attribute>
                     <Name>InputType</Name>
                     <Value>Diag</Value>
                  </Attribute>
                  <Attribute>
                     <Name>DiagType</Name>
                     <Value>初步诊断</Value>
                  </Attribute>
                  <Attribute>
                     <Name>DiagFormat</Name>
                     <Value>{DiagNo}.{FullName} </Value>
                  </Attribute>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>初步诊断-西医诊断名称</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000008661</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>107</InnerID>
               <ID>field11</ID>
               <ToolTip>【初   诊断-西医诊断名称】由医师根据患者就诊时的情况，综合分析所作出的西医诊断名称</ToolTip>
               <PropertyExpressions>
                  <Item Name="Visible">[诊断类型]='初步诊断'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <VisibleExpression>[诊断类型]='初步诊断'</VisibleExpression>
               <ContentReadonly>True</ContentReadonly>
               <Visible>false</Visible>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <Name>FC0000008661</Name>
               <BackgroundText>初步诊断-西医诊断名称</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString" StyleIndex="1" WhitespaceCount="1">
               <InnerID>1242</InnerID>
               <Text> </Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="1">
               <Attributes>
                  <Attribute>
                     <Name>InputType</Name>
                     <Value>Diag</Value>
                  </Attribute>
                  <Attribute>
                     <Name>DiagType</Name>
                     <Value>初步诊断</Value>
                  </Attribute>
                  <Attribute>
                     <Name>DiagFormat</Name>
                     <Value>,{DiagCode}</Value>
                  </Attribute>
               </Attributes>
               <InnerID>529</InnerID>
               <ID>field34</ID>
               <EnableValueValidate>true</EnableValueValidate>
               <Visible>false</Visible>
               <BorderElementColor />
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>108</InnerID>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>诊断依据及鉴别诊断</Value>
                  </Attribute>
               </Attributes>
               <InnerID>109</InnerID>
               <ID>FC0000009687</ID>
               <Deleteable>false</Deleteable>
               <Name>0.10</Name>
               <Width>524.9999</Width>
               <Height>49.90234</Height>
               <Text>三、诊断依据及鉴别诊断：</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>110</InnerID>
            </Element>
            <Element xsi:type="XString" StyleIndex="1" WhitespaceCount="4">
               <InnerID>1243</InnerID>
               <Text>    </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>诊断依据</Value>
                  </Attribute>
               </Attributes>
               <InnerID>112</InnerID>
               <ID>FC0000001109</ID>
               <Deleteable>false</Deleteable>
               <Name>0.10.1</Name>
               <Width>262.5</Width>
               <Height>49.90234</Height>
               <Text>1.诊断依据：</Text>
            </Element>
            <Element xsi:type="XString">
               <InnerID>1244</InnerID>
               <Text>老年男性，慢性病程，急性加重，主要表现为记忆力、理解力下降，肌张力增高、行动缓慢，考虑</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>113</InnerID>
            </Element>
            <Element xsi:type="XString" StyleIndex="1" WhitespaceCount="4">
               <InnerID>1245</InnerID>
               <Text>    </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <InnerID>115</InnerID>
               <ID>label13637638539839200259</ID>
               <Deleteable>false</Deleteable>
               <Name>0.10.2</Name>
               <Width>262.5</Width>
               <Height>49.90234</Height>
               <Text>2.鉴别诊断：</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>鉴别诊断-西医诊断名称</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001101</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>116</InnerID>
               <ID>field11637638539839200260</ID>
               <ToolTip>【鉴别诊断-西医诊断名称】需要进行鉴别的西医疾病诊断名称</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <XElements>
                  <Element xsi:type="XString">
                     <InnerID>1271</InnerID>
                     <Text>1、颅内占位：多有头晕、头痛，头颅CT有助于诊断；2、脑出血：可出现头痛，恶心、呕吐，脑膜刺激征阳性，头颅CT或核磁可排除</Text>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <Name>FC0000001101</Name>
               <InnerValue>1、颅内占位：多有头晕、头痛，头颅CT有助于诊断；2、脑出血：可出现头痛，恶心、呕吐，脑膜刺激征阳性，头颅CT或核磁可排除</InnerValue>
               <BackgroundText>鉴别诊断-西医诊断名称</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>117</InnerID>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>病情评估</Value>
                  </Attribute>
               </Attributes>
               <InnerID>118</InnerID>
               <ID>FC0000008656</ID>
               <Deleteable>false</Deleteable>
               <Name>0.15</Name>
               <Width>306.25</Width>
               <Height>49.90234</Height>
               <Text>四、病情评估：</Text>
            </Element>
            <Element xsi:type="XString">
               <InnerID>1246</InnerID>
               <Text>病情有加重可能。</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>119</InnerID>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>诊疗计划</Value>
                  </Attribute>
               </Attributes>
               <InnerID>120</InnerID>
               <ID>FC0000001110</ID>
               <Deleteable>false</Deleteable>
               <Name>0.20</Name>
               <Width>524.9999</Width>
               <Height>49.90234</Height>
               <Text>五、治疗原则及诊疗计划：</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>1742</InnerID>
            </Element>
            <Element xsi:type="XString" StyleIndex="1">
               <InnerID>1247</InnerID>
               <Text>1、完善血常规、C反应蛋白、降钙素原、尿便常规、血凝、肝功、肾功、电解质、心肌酶、血脂血糖、糖化血红蛋白、D二聚体了解有无血栓形成、完善血管内皮生长因子了解有无血管炎症、完善BNP了解心功能、完善血同型半胱氨酸了解叶酸水平、行氧化低密度脂蛋白了解动脉粥样硬化性疾病风险；阿司匹林及他汀药物基因检测、完善抗核抗体谱了解有无免疫性疾病所致。完善甲状腺功能及甲状腺彩超排除甲状腺疾病所致、心电图、完善腹部彩超了解上腹部情况、完善下肢血管彩超了解有无下肢静脉血栓形成、心脏彩超及颈部血管彩超、完善头颅CT排除脑出血、完善胸部CT了解肺部情况、完善动态心电图及动态血压了解有无阵发心律失常及了解24小时血压情况，完善TCD了解颅内血流，完善脑电图了解脑功能，完善肌电图检查，行颈椎MRI了解颈椎、颈髓情况，耳针治疗、头颅磁共   脑功能成像+头颅磁共振血管成像了解颅内病变及颅内血管情况等常规检查，明确病因并指导下一步治疗方案；</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>960</InnerID>
            </Element>
            <Element xsi:type="XString" StyleIndex="1">
               <InnerID>1248</InnerID>
               <Text>2、诊疗计划：给予吸氧、心电监护、抑制血小板聚集、稳定斑块、改善脑侧枝循环、改善脑供血、监测血压、监测血糖及对症支持治疗。</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>1022</InnerID>
            </Element>
            <Element xsi:type="XString" StyleIndex="1">
               <InnerID>1249</InnerID>
               <Text>诊疗中的注意事项及医患沟通要点、评价：症状反复、再发脑梗死、心肌梗死、呛咳窒息、肺部感染、下肢静脉血栓、多脏器衰竭、药物不良反应等</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>122</InnerID>
            </Element>
            <Element xsi:type="XString">
               <InnerID>1250</InnerID>
               <Text>                                               			      </Text>
            </Element>
            <Element xsi:type="XInputField">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>住院医师签名</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001115</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignLevel</Name>
                     <Value>EMR_AUDIT_RESIDENT</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignVerify</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>1</Value>
                  </Attribute>
               </Attributes>
               <InnerID>642</InnerID>
               <ID>field49</ID>
               <ToolTip>【住院医师签名】患者入院时所在科室具体负责诊治的，具有住院医师   业技术职务任职资格的医师签署的在公安户籍管理部门正式登记注册的姓氏和名称</ToolTip>
               <PropertyExpressions>
                  <Item Name="Visible">[诊断类型]='入院诊断'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <VisibleExpression>[诊断类型]='入院诊断'</VisibleExpression>
               <XElements>
                  <Element xsi:type="XImage">
                     <InnerID>1210</InnerID>
                     <ContentReadonly>True</ContentReadonly>
                     <Width>200</Width>
                     <Height>80</Height>
                     <CompressSaveMode>false</CompressSaveMode>
                     <Image>
                        <ImageDataBase64String> R0lGODlh gQAdAPcA AP8AAP8A Ov86AP86 Ov8AZv86 Zv9mAP9m Ov9mZv86 kP9mkP9m tv+QOv+Q Zv+2Zv+Q kP+Qtv+2 kP+2tv+Q 2/+22/+2 ///bkP/b tv//tv/b 2//b////
 2////wAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA
 AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA
 AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA
 AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA
 AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA
 AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA
 AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA AAAAAAAA
 AAAAAAAA AAAAAAAA ACH5BAEA ABwALAAA AACBAB0A AAj/ADkI HEhQYAYJ GgoqXMiw ocOHECNK nEiRgwEA Aygs3IAA AYSEFUOK HEmy4kUA ABYodIAS QACQJSFa EIAyY8yb
 JTfQRKlA I0EGLQFM wPmQZdAC NyMMGFDA J1GHGSwE balSoFGU CyQgfCpw w4UGO6dW HYlBLNeC DTBOPapg 4NWpBWBO zEC34Nug AxAokBtS 6tQKCjfU jXlhwFoF GQpeOLAW
 K8XFZgUC hcuXpN+W BBZeDQAh ZoYIOwlo TRiBcWOX TiGmbZzA 7em2Nzco XYuU4NvM IRNz2GB6 agAFvX2n hgjZd2W6 wYfivIuy 9cDLAJxT PBAAdMsB PaFXnTyV QOWHq1EC
 /2b4dsDH m8GDjucA XTnFkwEk lF5aIDgF 641hTyxL dWGGB2EF 9RJJvJ3m 3XPqhcRc Y4ad1pSC mBE022ko BTBWRBd0 pNEFAQaV gFybffcQ fwng110E FB6lm0wt tSXYakvl
 hUCAcYXE 3VoBLEXB BQ84dRVu EXR0HkRA BdBhhQkJ xmGKADxI JF4QgMZZ YsWhhFtF OlGoHH8D cvBjV2HV +NCN14lZ ZUsF9JjQ VetB9UBv BWwQ3mES UCAieb7t 1CV3ynEn
 HXTSQZUe kyi552VL hj6UZaHs ERpAog6R GdQCCXGn 0qLRIYgo cekhJhlP GvHpGpKB pYcdipsu 2FibDyk1 wASSKu0Q Xmv8NTeq eAzJGWAA m+KJUmdV dklQrUwW AMGcQSFQ
 J113OoSp g286Wllh cGWwaADD 3Urbdw0g ABJdEiDQ 4GlTXrDV TW9NMCih LQkrEKo1 dQamlQ1B V6GnFd3Y 0ZEB6KUV XYNRRCxu F5iIkrEP IKBcVB5S tGigBdUK qbO6fRYW
 tgR9dSST 2ImIqbtG cdYQpqw+ mVJDtZYM VQPjuuTk RgYTeuFu FvcX2IoN sYQxljTN TNCiKp81 0GfiBpgX BDgTdBJP zQqtmAQP XeSu0ziZ K0HSVN8E FMRZd+21 SCz5/PXY
 ZJdt9tlo p6322mwL HRAAOw==</ImageDataBase64String>
                     </Image>
                  </Element>
               </XElements>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <UserEditable>false</UserEditable>
               <Name>FC0000001115</Name>
               <InnerValue>24121605</InnerValue>
               <BackgroundText>住院医师签名</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString" WhitespaceCount="1">
               <InnerID>1251</InnerID>
               <Text> </Text>
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <InnerID>124</InnerID>
               <ID>label21</ID>
               <Deleteable>false</Deleteable>
               <Name>999</Name>
               <Width>15</Width>
               <Height>46</Height>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>上级医师签名</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001107</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignLevel</Name>
                     <Value>EMR_AUDIT_ATTEND</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignVerify</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>1</Value>
                  </Attribute>
               </Attributes>
               <InnerID>125</InnerID>
               <ID>field19</ID>
               <ToolTip>【上级医师签名】具有主治医师以上专业技术职务资格的主管医师签署的在公安户籍管理部门正式登记注册的姓氏和   称</ToolTip>
               <PropertyExpressions>
                  <Item Name="Visible">[诊断类型]='初步诊断'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <VisibleExpression>[诊断类型]='初步诊断'</VisibleExpression>
               <Visible>false</Visible>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <UserEditable>false</UserEditable>
               <Name>FC0000001107</Name>
               <BackgroundText>上级医师审签</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <InnerID>693</InnerID>
               <ID>field45</ID>
               <PropertyExpressions>
                  <Item Name="Visible">[诊断类型]='初步诊断'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <VisibleExpression>[诊断类型]='初步诊断'</VisibleExpression>
               <XElements>
                  <Element xsi:type="XString" StyleIndex="3">
                     <InnerID>1272</InnerID>
                     <Text>/</Text>
                  </Element>
               </XElements>
               <Visible>false</Visible>
               <BorderElementColor />
               <InnerValue>/</InnerValue>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>住院医师签名</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001115</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignLevel</Name>
                     <Value>EMR_AUDIT_RESIDENT</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignVerify</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>1</Value>
                  </Attribute>
               </Attributes>
               <InnerID>127</InnerID>
               <ID>field8</ID>
               <ToolTip>【住院医师签名】患者入院时所在科室具体负责诊治的，具有住院医师专业技术职务任职资格的医师签署的在公安户籍管理部门正式登记注册的姓氏和名称</ToolTip>
               <PropertyExpressions>
                  <Item Name="Visible">[诊断类型]='初步诊断'</Item>
               </PropertyExpressions>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <VisibleExpression>[诊断类型]='初步诊断'</VisibleExpression>
               <Visible>false</Visible>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <UserEditable>false</UserEditable>
               <Name>FC0000001115</Name>
               <BackgroundText>双击签名</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag" StyleIndex="10">
               <InnerID>128</InnerID>
            </Element>
         </XElements>
      </Element>
      <Element xsi:type="XTextFooter">
         <InnerID>129</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="HorizontalLine">
               <InnerID>130</InnerID>
               <ID>hl1</ID>
            </Element>
            <Element xsi:type="XPageInfo" StyleIndex="0">
               <InnerID>131</InnerID>
               <Width>100</Width>
               <AutoHeight>true</AutoHeight>
               <Height>54.89257</Height>
            </Element>
            <Element xsi:type="XParagraphFlag" StyleIndex="2">
               <InnerID>132</InnerID>
            </Element>
         </XElements>
      </Element>
   </XElements>
   <Parameters>
      <Parameter Name="TI00000210" />
      <Parameter Name="TI00000206" />
      <Parameter Name="TI00000212" />
   </Parameters>
   <FileName>D:\ekingsoft\doctor5\ekingemr\tempdoc\8a6a8a14047d4503953af3c8c7bf4ff0.xml</FileName>
   <FileFormat>XML</FileFormat>
   <UserHistories>
      <History>
         <ID>24121605</ID>
         <Name>韩廷灿</Name>
         <SavedTime>2025-07-09T22:02:39.8882583+08:00</SavedTime>
         <PermissionLevel>1</PermissionLevel>
         <ClientName>{"Ip":"***********","Mac":"28C5C882F0AF","MachineName":"PC-202411181031"}</ClientName>
      </History>
   </UserHistories>
   <ContentStyles>
      <Default xsi:type="DocumentContentStyle">
         <FontName>宋体</FontName>
         <FontSize>10.5</FontSize>
      </Default>
      <Styles>
         <Style Index="0">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
         </Style>
         <Style Index="1">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <LineSpacingStyle>SpaceMultiple</LineSpacingStyle>
            <LineSpacing>1.1</LineSpacing>
         </Style>
         <Style Index="2">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <Align>Center</Align>
         </Style>
         <Style Index="3">
            <BackgroundColor>#00000000</BackgroundColor>
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
         </Style>
         <Style Index="4">
            <BackgroundColor>#00000000</BackgroundColor>
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <LineSpacingStyle>SpaceMultiple</LineSpacingStyle>
            <LineSpacing>1.1</LineSpacing>
         </Style>
         <Style Index="5">
            <BackgroundColor>#FFFFFF</BackgroundColor>
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
         </Style>
         <Style Index="6">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <LineSpacingStyle>Space1pt5</LineSpacingStyle>
            <Align>Center</Align>
         </Style>
         <Style Index="7">
            <BackgroundColor>#00000000</BackgroundColor>
            <FontName>宋体</FontName>
            <FontSize>18</FontSize>
            <Bold>true</Bold>
         </Style>
         <Style Index="8">
            <FontName>宋体</FontName>
            <FontSize>18</FontSize>
            <Bold>true</Bold>
            <Align>Center</Align>
         </Style>
         <Style Index="9">
            <BackgroundColor>#00000000</BackgroundColor>
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <Bold>true</Bold>
         </Style>
         <Style Index="10">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <Align>Right</Align>
         </Style>
      </Styles>
   </ContentStyles>
   <Info>
      <ShowHeaderBottomLine>True</ShowHeaderBottomLine>
      <LicenseText>江苏鑫亿软件股份有限公司:鹤壁市人民医院</LicenseText>
      <CreationTime>1980-01-01T00:00:00</CreationTime>
      <LastModifiedTime>2025-07-09T22:02:39.8882583+08:00</LastModifiedTime>
      <LastPrintTime>1980-01-01T00:00:00</LastPrintTime>
      <Operator>DCSoft.Writer Version:1.2015.3.18</Operator>
      <NumOfPage>2</NumOfPage>
   </Info>
   <BodyText>2025年07月09日 22时01分               首次病程记录
    姓名：程太生，性别：男，年龄：75岁，因“认知功能下降半年，加重1周”入院。
一、病例特点：
    1.患者特点：
    2.现病史：近半年来记忆力、理解力下降，近1周来明显加重，做饭糊锅、不知道放调料，忘记怎么使用空调电视遥控器，忘记早餐吃过什么，经门诊以“认知障碍” 收住入院。发病以来，精神状态尚可，食欲正常，睡眠正常，大便正常，小便正常，体重无变化
    3.既往史：既往身体健康，否认高血压,糖尿病,冠心病-心肌梗死,脑梗死,高胆固醇血症等疾病史。无甲状腺功能异常，否认病毒性肝炎、肺结核等传染病史；无手术史；无外伤史；无输血史；无过敏史；预防接种史不详。
    4.体格检查：T:36℃ P:72次/分 R:18次/分 BP:140/83mmHg,发育正常,色泽正常,胸廓对称,无畸形,双肺呼吸音清晰,未闻及干、湿性啰音,未闻及胸膜摩擦音,无哮鸣音,心脏相对浊音界无扩大,心率72次/分,心律齐心音正常,各瓣膜听诊区未闻及病理性杂音,腹部平坦,腹壁无静脉曲张,腹部平坦,无胃肠蠕动波,腹壁柔软,腹部无压痛,无反跳痛,墨菲氏征阴性,双肾无叩击痛,生理反射存在,病理反射阴性,脑膜刺激征阴性。
      专科情况：意识清醒，语言清晰，瞬时记忆力正常，短时记忆力正常，长时记忆力正常，理解力正常，计算力正常，判断力正常，定向力正常，右利手。颅神经：        嗅神经：左正常，右正常。    视神经：近视力：左正常，右正常；远视力：左正常，右正常；视野（粗测）无缺损。    动眼神经、滑车神经、外展神经：睑下垂：   无，右无；眼球形态正常，位置居中，复视无。瞳孔直径：左3.0mm,右3.0mm,形态左正常，右正常。眼球震颤：无，眼球运动：正常。    三叉神经：感觉：左正常，右正常；角膜反射：直接：左正常，右正常；间接：左正常，右正常。运动：下颌偏向：无；嚼肌正常，颞肌正常；下颌反射：正常。    面神经：眼裂：左正常，右正常。鼻唇沟：左正常，右正常。闭目：左正常，右正常；皱额：左正常，右正常；鼓气、露齿、吹口哨：正常。    位听神经：耳语：左正常，右正常。    舌咽神经、迷走神经：发音正常，味觉感觉正常；咽反射：左正常，右正常。软腭动度：左正常，右正常；悬雍垂：居中。    副神经：胸锁乳突肌、斜方肌：左正常，右正常。    舌下神经：伸舌偏向：正常；萎缩：左无，右无；舌肌纤颤：左无，右无。运动系统    自行走入院入院，步态正常，肌肉萎缩：无，肌束颤动：无。    肌力：左：肩关节5级；肘关节5级；腕关节5级；指关节5级；          右：肩关节5级；肘关节5级；腕关节5级；指关节5级；          左：髋关节5级；膝关节5级；踝关节5级；趾关节5级；          右：髋关节5级；膝关节5级；踝关节5级；趾关节5级；    肌张力：正常。    不自主运动：无。    共济运动：指鼻试验：左正常，右正常；快复动作：左正常，右正常；快速轮替动作：左正常，右正常；肌反击现象：左正常，右正常；跟膝胫试验：左正常，右正常。闭目难立征：阴性。感觉系统    痛温觉障碍部位无，程度无，触觉障碍部位无，程度无。    位置觉：正常；震动觉：正常；运动觉：正常。皮肤定位觉：正常；两点辨别觉：正常；实体觉：正常；体表图形觉：正常。    神经反射（消失- 降低+ 正常++ 活跃+++ 亢进++++）左右左右腹壁反射上（T7-8）++++肱三头肌反射（C6-7）++++       中（T9-10）++++桡骨膜反射（C5-8）++++       下（T11-12）++++膝反射（L2-4）++++肱二头肌反射（C5-6）++++跟腱反射（S1-2）++++Babinski征--Gordon征--Oppenheim征--Hoffmann征--    脑膜刺激征：颈强直：阴性。kernig征：阴性；Brudzinski征：阴性，    神经干牵拉试验：Lasegue征：左侧阴性，右侧阴性。    其他：无。 临床评估表            VTE风险评估：2分
    5.辅助检查：暂无
二、入院诊断：1.认知障碍
2.肌张力障碍  
三、诊断依据及鉴别诊断：
    1.诊断依据：老年男性，慢性病程，急性加重，主要表现为记忆力、理解力下降，肌张力增高、行动缓慢，考虑
    2.鉴别诊断：1、颅内占位：多有头晕、头痛，头颅CT有助于诊断；2、脑出血：可出现头痛，恶心、呕吐，脑膜刺激征阳性，头颅CT或核磁可排除
四、病情评估：病情有加重可能。
五、治疗原则及诊疗计划：
1、完善血常规、C反应蛋白、降钙素原、尿便常规、血凝、肝功、肾功、电解质、心肌酶、血脂血糖、糖化血红蛋白、D二聚体了解有无血栓形成、完善血管内皮生长因子了解有无血管炎症、完善BNP了解心功能、完善血同型半胱氨酸了解叶酸水平、行氧化低密度脂蛋白了解动脉粥样硬化性疾病风险；阿司匹林及他汀药物基因检测、完善抗核抗体谱了解有无免疫性疾病所致。完善甲状腺功能及甲状腺彩超排除甲状腺疾病所致、心电图、完善腹部彩超了解上腹部情况、完善下肢血管彩超了解有无下肢静脉血栓形成、心脏彩超及颈部血管彩超、完善头颅CT排除脑出血、完善胸部CT了解肺部情况、完善动态心电图及动态血压了解有无阵发心律失常及了解24小时血压情况，完善TCD了解颅内血流，完善脑电图了解脑功能，完善肌电图检查，行颈椎MRI了解颈椎、颈髓情况，耳针治疗、头颅磁共振脑功能成像+头颅磁共振血管成像了解颅内病变及颅内血管情况等常规检查，明确病因并指导下一步治疗方案；
2、诊疗计划：给予吸氧、心电监护、抑制血小板聚集、稳定斑块、改善脑   枝循环、改善脑供血、监测血压、监测血糖及对症支持治疗。
诊疗中的注意事项及医患沟通要点、评价：症状反复、再发脑梗死、心肌梗死、呛咳窒息、肺部感染、下肢静脉血栓、多脏器衰竭、药物不良反应等
                                               			       </BodyText>
   <LocalConfig />
   <PageSettings>
      <PaperKind>Custom</PaperKind>
      <PaperWidth>716</PaperWidth>
      <PaperHeight>1043</PaperHeight>
      <LeftMargin>35</LeftMargin>
      <TopMargin>164</TopMargin>
      <RightMargin>35</RightMargin>
      <BottomMargin>59</BottomMargin>
   </PageSettings>
</XTextDocument>
0
