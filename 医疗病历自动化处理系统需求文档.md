# 医疗病历自动化处理系统需求文档

## 1. 项目概述

### 1.1 项目背景
基于对现有医疗信息系统的抓包分析，开发一个基于Python的医疗病历自动化处理系统，实现与医院HIS系统的无缝对接，通过LLM技术自动填写病历内容，提高医生工作效率。

### 1.2 项目目标
- 实现与医院服务器的安全认证和通信
- 自动获取和管理病历模板
- 智能填写病历内容
- 提供完整的病历生命周期管理

### 1.3 技术栈
- **编程语言**: Python 3.8+
- **HTTP客户端**: httpx
- **协议**: HTTP/1.1
- **数据格式**: JSON, XML
- **AI集成**: LLM API (OpenAI/本地模型)
- **数据处理**: lxml, json

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面层    │    │   业务逻辑层    │    │   数据访问层    │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ CLI/GUI界面     │    │ 认证管理模块    │    │ HTTP客户端      │
│ 配置管理        │    │ 模板管理模块    │    │ 数据解析器      │
│ 日志显示        │    │ 患者信息模块    │    │ 缓存管理        │
└─────────────────┘    │ 病历处理模块    │    └─────────────────┘
                       │ LLM集成模块     │
                       └─────────────────┘
```

### 2.2 核心模块设计

#### 2.2.1 认证管理模块 (AuthManager)
- OAuth2.0客户端认证
- Bearer Token管理
- 会话保持和刷新

#### 2.2.2 模板管理模块 (TemplateManager)
- 病历模板获取和缓存
- XML模板解析
- FC Code映射管理

#### 2.2.3 患者信息模块 (PatientManager)
- 患者基本信息查询
- 住院信息管理
- 诊断信息获取

#### 2.2.4 病历处理模块 (MedicalRecordProcessor)
- 病历创建和编辑
- XML内容生成
- 病历保存和提交

#### 2.2.5 LLM集成模块 (LLMIntegrator)
- 智能内容生成
- 上下文管理
- 提示词模板管理

## 3. 详细功能规格说明

### 3.1 服务器认证功能

#### 3.1.1 功能描述
实现与医院HIS系统的双重认证机制

#### 3.1.2 技术实现
```python
# 第一步：客户端认证
POST /authserver/oauth/token
Authorization: Basic ZWtpbmdoaXMtZG9jdG9yLWVtcjoxMjM=
Content-Type: application/x-www-form-urlencoded
Body: grant_type=client_credentials

# 第二步：用户认证
POST /manager/auth-ext/auth
Authorization: Bearer {access_token}
Content-Type: application/x-www-form-urlencoded
Body: authType=ekinghis_login&basic=Basic+{encoded}&cred={user_id}
```

#### 3.1.3 返回数据结构
```json
{
  "code": "SUCCESS",
  "data": {
    "dataInfo": {
      "access_token": "eyJhbGciOiJSUzI1NiIs...",
      "refresh_token": "eyJhbGciOiJSUzI1NiIs...",
      "expires_in": 43199,
      "userId": "1066434522704248832",
      "userName": "24121605"
    }
  }
}
```

### 3.2 病历模板管理功能

#### 3.2.1 功能描述
获取和管理可用的病历模板列表，支持按科室和类型筛选

#### 3.2.2 API接口
```python
# 获取病历模板列表
GET /emr/index/tpl?deptCode={dept_code}&createrNo={creator}&visitId={visit_id}&docType={doc_type}&type={type}
```

#### 3.2.3 模板分类
- **type=1**: 基础病历模板（入院记录、出院记录等）
- **type=2**: 病程记录模板（查房记录、日常病程等）

### 3.3 患者信息管理功能

#### 3.3.1 功能描述
获取患者基本信息、住院信息和诊断信息

#### 3.3.2 API接口
```python
# 获取患者基本信息
POST /manager/processToken/test_process_data?funCode=GetPATINFO&funParams={params}
```

#### 3.3.3 患者信息结构
```json
{
  "ZZ": "河南省鹤壁市",      // 住址
  "XM": "刘荣只",           // 姓名
  "XB": "女",              // 性别
  "NL": 66,                // 年龄
  "ZYH": "574703",         // 住院号
  "JZKS": "神经内科总院",   // 就诊科室
  "ZD": "脑梗死"           // 诊断
}
```

### 3.4 XML模板处理功能

#### 3.4.1 功能描述
解析XML格式的病历模板，识别需要填写的字段

#### 3.4.2 FC Code映射
系统通过FC Code定位XML中的可编辑字段：
- `FC0000001099`: 患者姓名
- `FC0000001108`: 性别代码
- `FC0000001105`: 年龄
- `FC0000001113`: 主诉
- `FC0000000850`: 现病史内容

#### 3.4.3 XML结构示例
```xml
<Element xsi:type="XInputField">
  <Attributes>
    <Attribute>
      <Name>FieldCode</Name>
      <Value>FC0000001113</Value>
    </Attribute>
    <Attribute>
      <Name>FieldName</Name>
      <Value>主诉</Value>
    </Attribute>
  </Attributes>
  <InnerValue>认知功能下降半年，加重1周</InnerValue>
</Element>
```

### 3.5 智能填写功能

#### 3.5.1 功能描述
基于患者信息和病历模板，使用LLM自动生成病历内容

#### 3.5.2 提示词模板
```python
PROMPT_TEMPLATES = {
    "主诉": "根据患者{age}岁{gender}，诊断为{diagnosis}，生成合适的主诉内容",
    "现病史": "基于主诉：{chief_complaint}，患者年龄{age}岁，诊断{diagnosis}，详细描述现病史",
    "查房记录": "患者{name}，{age}岁{gender}，住院号{admission_no}，诊断{diagnosis}，生成主治医师查房记录"
}
```

### 3.6 病历保存功能

#### 3.6.1 功能描述
将填写完成的病历保存到服务器

#### 3.6.2 保存流程
1. **文件上传**: 上传XML文件到OSS
2. **病历索引**: 创建病历索引记录
3. **权限验证**: 验证操作权限

#### 3.6.3 API调用序列
```python
# 1. 上传XML文件
POST /oss/file/upload
Content-Type: multipart/form-data

# 2. 创建病历记录
POST /emr/index
Content-Type: application/json

# 3. 验证权限
GET /emr/index/auth?id={index_id}&empNo={emp_no}
```

## 4. API接口定义

### 4.1 认证接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 客户端认证 | POST | /authserver/oauth/token | 获取客户端访问令牌 |
| 用户认证 | POST | /manager/auth-ext/auth | 用户身份验证 |

### 4.2 模板管理接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 获取模板列表 | GET | /emr/index/tpl | 获取病历模板列表 |
| 获取模板内容 | GET | /oss/file/show | 获取XML模板内容 |

### 4.3 患者信息接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 患者基本信息 | POST | /manager/processToken/test_process_data | 获取患者详细信息 |

### 4.4 病历操作接口

| 接口 | 方法 | 路径 | 描述 |
|------|------|------|------|
| 上传病历文件 | POST | /oss/file/upload | 上传XML病历文件 |
| 创建病历记录 | POST | /emr/index | 创建病历索引 |
| 验证操作权限 | GET | /emr/index/auth | 验证用户权限 |

## 5. 数据流程图

```mermaid
graph TD
    A[系统启动] --> B[客户端认证]
    B --> C[用户登录认证]
    C --> D[获取患者信息]
    D --> E[获取病历模板列表]
    E --> F[选择模板]
    F --> G[下载XML模板]
    G --> H[解析FC Code字段]
    H --> I[调用LLM生成内容]
    I --> J[填充XML模板]
    J --> K[上传XML文件]
    K --> L[创建病历记录]
    L --> M[验证权限]
    M --> N[保存完成]
```

## 6. 开发里程碑和时间规划

### 第一阶段：基础框架开发 (2周)
- [ ] 项目结构搭建
- [ ] HTTP客户端封装
- [ ] 认证模块开发
- [ ] 配置管理系统

### 第二阶段：核心功能开发 (3周)
- [ ] 模板管理模块
- [ ] 患者信息模块
- [ ] XML解析器
- [ ] FC Code映射系统

### 第三阶段：AI集成开发 (2周)
- [ ] LLM接口集成
- [ ] 提示词模板系统
- [ ] 内容生成逻辑
- [ ] 质量控制机制

### 第四阶段：病历处理开发 (2周)
- [ ] 病历创建功能
- [ ] 文件上传模块
- [ ] 保存流程实现
- [ ] 错误处理机制

### 第五阶段：测试和优化 (1周)
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能优化
- [ ] 文档完善

## 7. 风险评估和缓解策略

### 7.1 技术风险

#### 7.1.1 API变更风险
**风险**: 医院HIS系统API接口可能发生变更
**缓解策略**: 
- 实现灵活的配置系统
- 建立API版本兼容机制
- 定期更新抓包分析

#### 7.1.2 认证失效风险
**风险**: Token过期或认证机制变更
**缓解策略**:
- 实现自动Token刷新
- 建立重连机制
- 多重认证备份方案

### 7.2 数据安全风险

#### 7.2.1 患者隐私保护
**风险**: 患者敏感信息泄露
**缓解策略**:
- 数据传输加密
- 本地数据脱敏
- 访问权限控制

#### 7.2.2 系统安全风险
**风险**: 未授权访问或数据篡改
**缓解策略**:
- 强化身份验证
- 操作日志记录
- 数据完整性校验

### 7.3 业务风险

#### 7.3.1 AI生成内容质量
**风险**: LLM生成的病历内容不准确
**缓解策略**:
- 建立内容审核机制
- 提供人工校对功能
- 持续优化提示词

#### 7.3.2 系统依赖风险
**风险**: 依赖的外部服务不可用
**缓解策略**:
- 实现离线模式
- 建立服务降级机制
- 多供应商备选方案

## 8. 质量保证措施

### 8.1 代码质量
- 遵循PEP 8编码规范
- 实现完整的单元测试覆盖
- 使用类型注解提高代码可读性
- 定期代码审查

### 8.2 系统可靠性
- 实现完善的错误处理机制
- 建立系统监控和告警
- 提供详细的操作日志
- 支持系统状态检查

### 8.3 用户体验
- 提供清晰的操作界面
- 实现进度提示功能
- 支持操作撤销和重试
- 提供详细的帮助文档

## 9. 部署和维护

### 9.1 部署要求
- Python 3.8+ 运行环境
- 网络连接到医院HIS系统
- LLM API访问权限
- 足够的存储空间

### 9.2 维护计划
- 定期更新依赖包
- 监控系统运行状态
- 备份重要配置数据
- 用户培训和技术支持

## 10. 技术实现细节

### 10.1 项目结构
```
medical_record_automation/
├── src/
│   ├── auth/
│   │   ├── __init__.py
│   │   ├── oauth_client.py      # OAuth2认证客户端
│   │   └── token_manager.py     # Token管理器
│   ├── templates/
│   │   ├── __init__.py
│   │   ├── template_manager.py  # 模板管理器
│   │   ├── xml_parser.py        # XML解析器
│   │   └── fc_code_mapper.py    # FC Code映射器
│   ├── patients/
│   │   ├── __init__.py
│   │   ├── patient_manager.py   # 患者信息管理
│   │   └── patient_models.py    # 患者数据模型
│   ├── records/
│   │   ├── __init__.py
│   │   ├── record_processor.py  # 病历处理器
│   │   ├── xml_generator.py     # XML生成器
│   │   └── record_models.py     # 病历数据模型
│   ├── llm/
│   │   ├── __init__.py
│   │   ├── llm_client.py        # LLM客户端
│   │   ├── prompt_templates.py  # 提示词模板
│   │   └── content_generator.py # 内容生成器
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── http_client.py       # HTTP客户端封装
│   │   ├── config.py            # 配置管理
│   │   ├── logger.py            # 日志管理
│   │   └── exceptions.py        # 自定义异常
│   └── main.py                  # 主程序入口
├── tests/
│   ├── test_auth/
│   ├── test_templates/
│   ├── test_patients/
│   ├── test_records/
│   └── test_llm/
├── config/
│   ├── settings.yaml            # 系统配置
│   ├── fc_code_mapping.json     # FC Code映射配置
│   └── prompt_templates.yaml    # 提示词模板配置
├── docs/
│   ├── api_reference.md         # API参考文档
│   ├── user_guide.md            # 用户指南
│   └── development_guide.md     # 开发指南
├── requirements.txt             # 依赖包列表
├── setup.py                     # 安装脚本
└── README.md                    # 项目说明
```

### 10.2 核心类设计

#### 10.2.1 认证管理器
```python
class AuthManager:
    """认证管理器，处理OAuth2认证和Token管理"""

    def __init__(self, base_url: str, client_id: str, client_secret: str):
        self.base_url = base_url
        self.client_id = client_id
        self.client_secret = client_secret
        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = None

    async def authenticate_client(self) -> str:
        """客户端认证，获取访问令牌"""
        pass

    async def authenticate_user(self, user_id: str) -> dict:
        """用户认证，获取用户令牌"""
        pass

    async def refresh_access_token(self) -> str:
        """刷新访问令牌"""
        pass

    def is_token_valid(self) -> bool:
        """检查令牌是否有效"""
        pass
```

#### 10.2.2 模板管理器
```python
class TemplateManager:
    """病历模板管理器"""

    def __init__(self, http_client: HttpClient):
        self.http_client = http_client
        self.template_cache = {}

    async def get_template_list(self, dept_code: str, doc_type: int, template_type: int) -> List[Template]:
        """获取模板列表"""
        pass

    async def get_template_content(self, file_id: str) -> str:
        """获取模板XML内容"""
        pass

    def parse_template_fields(self, xml_content: str) -> List[TemplateField]:
        """解析模板中的可编辑字段"""
        pass
```

#### 10.2.3 LLM集成器
```python
class LLMIntegrator:
    """LLM集成器，处理AI内容生成"""

    def __init__(self, llm_client: LLMClient, prompt_manager: PromptManager):
        self.llm_client = llm_client
        self.prompt_manager = prompt_manager

    async def generate_content(self, field_code: str, context: dict) -> str:
        """根据字段代码和上下文生成内容"""
        pass

    def build_prompt(self, template_name: str, context: dict) -> str:
        """构建提示词"""
        pass

    async def validate_content(self, content: str, field_type: str) -> bool:
        """验证生成内容的质量"""
        pass
```

### 10.3 配置文件示例

#### 10.3.1 系统配置 (settings.yaml)
```yaml
# 服务器配置
server:
  base_url: "http://11.0.10.25:4431"
  timeout: 30
  max_retries: 3

# 认证配置
auth:
  client_id: "ekinghis-doctor-emr"
  client_secret: "123"
  token_refresh_threshold: 300  # 提前5分钟刷新

# LLM配置
llm:
  provider: "openai"  # openai, azure, local
  model: "gpt-4"
  api_key: "${LLM_API_KEY}"
  max_tokens: 2000
  temperature: 0.3

# 缓存配置
cache:
  template_cache_ttl: 3600  # 1小时
  patient_cache_ttl: 1800   # 30分钟

# 日志配置
logging:
  level: "INFO"
  file: "logs/medical_record_automation.log"
  max_size: "10MB"
  backup_count: 5
```

#### 10.3.2 FC Code映射配置 (fc_code_mapping.json)
```json
{
  "patient_info": {
    "FC0000001099": {
      "field_name": "患者姓名",
      "data_type": "string",
      "required": true,
      "source": "patient.name"
    },
    "FC0000001108": {
      "field_name": "性别代码",
      "data_type": "string",
      "required": true,
      "source": "patient.gender",
      "mapping": {"男": "M", "女": "F"}
    },
    "FC0000001105": {
      "field_name": "年龄(岁)",
      "data_type": "number",
      "required": true,
      "source": "patient.age"
    }
  },
  "medical_content": {
    "FC0000001113": {
      "field_name": "主诉",
      "data_type": "string",
      "required": true,
      "llm_template": "chief_complaint",
      "max_length": 200
    },
    "FC0000000850": {
      "field_name": "现病史内容",
      "data_type": "text",
      "required": true,
      "llm_template": "present_illness",
      "max_length": 2000
    }
  }
}
```

#### 10.3.3 提示词模板配置 (prompt_templates.yaml)
```yaml
templates:
  chief_complaint:
    name: "主诉生成"
    prompt: |
      根据以下患者信息生成合适的主诉：
      - 患者：{patient_name}，{age}岁，{gender}
      - 诊断：{diagnosis}
      - 科室：{department}

      要求：
      1. 主诉应简洁明了，突出主要症状
      2. 包含症状持续时间
      3. 符合医学表达习惯
      4. 字数控制在50字以内

      主诉：

  present_illness:
    name: "现病史生成"
    prompt: |
      根据以下信息生成详细的现病史：
      - 患者：{patient_name}，{age}岁，{gender}
      - 主诉：{chief_complaint}
      - 诊断：{diagnosis}
      - 入院方式：{admission_type}

      要求：
      1. 详细描述症状的发生、发展过程
      2. 包含相关的阴性症状
      3. 描述就诊经过和治疗情况
      4. 符合病史书写规范
      5. 字数控制在500字以内

      现病史：

  ward_round_record:
    name: "查房记录生成"
    prompt: |
      生成主治医师查房记录：
      - 患者：{patient_name}，{age}岁，{gender}
      - 住院号：{admission_no}
      - 诊断：{diagnosis}
      - 入院天数：{admission_days}天
      - 当前治疗：{current_treatment}

      要求：
      1. 包含患者当前病情评估
      2. 描述治疗效果和病情变化
      3. 提出下一步治疗计划
      4. 符合查房记录格式
      5. 字数控制在300字以内

      查房记录：
```

### 10.4 错误处理和异常管理

#### 10.4.1 自定义异常类
```python
class MedicalRecordException(Exception):
    """医疗病历系统基础异常"""
    pass

class AuthenticationError(MedicalRecordException):
    """认证错误"""
    pass

class TemplateNotFoundError(MedicalRecordException):
    """模板未找到错误"""
    pass

class PatientNotFoundError(MedicalRecordException):
    """患者未找到错误"""
    pass

class LLMGenerationError(MedicalRecordException):
    """LLM生成错误"""
    pass

class XMLParsingError(MedicalRecordException):
    """XML解析错误"""
    pass
```

#### 10.4.2 重试机制
```python
import asyncio
from functools import wraps

def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    await asyncio.sleep(delay * (2 ** attempt))
            return None
        return wrapper
    return decorator
```

### 10.5 性能优化策略

#### 10.5.1 缓存机制
- **模板缓存**: 缓存常用病历模板，减少网络请求
- **患者信息缓存**: 临时缓存患者信息，避免重复查询
- **LLM响应缓存**: 缓存相似请求的LLM响应

#### 10.5.2 并发处理
- 使用异步编程提高I/O效率
- 实现连接池管理HTTP连接
- 支持批量处理多个病历

#### 10.5.3 资源管理
- 实现内存使用监控
- 定期清理过期缓存
- 优化XML解析性能

### 10.6 安全措施

#### 10.6.1 数据加密
- 传输层使用HTTPS加密
- 敏感配置信息加密存储
- 患者信息脱敏处理

#### 10.6.2 访问控制
- 实现基于角色的权限控制
- 记录所有操作日志
- 定期审计系统访问

#### 10.6.3 数据完整性
- 实现数据校验机制
- 支持操作回滚
- 建立数据备份策略

---

**文档版本**: v1.0
**创建日期**: 2025-08-19
**最后更新**: 2025-08-19
**负责人**: 开发团队
