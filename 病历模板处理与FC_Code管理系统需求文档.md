# 病历模板处理与FC Code管理系统需求文档

## 1. 系统概述

### 1.1 项目背景
病历模板处理与FC Code管理系统是医疗病历自动化处理系统的核心组件，专门负责病历模板的解析、FC Code字段的管理以及LLM提示词的精确映射和调用。

### 1.2 系统目标
- 实现病历模板的智能解析和处理
- 建立FC Code字段的统一管理机制
- 提供精确的LLM提示词映射和调用服务
- 支持模板版本管理和动态更新

### 1.3 核心功能
1. **病历模板解析引擎**：解析XML格式的病历模板
2. **FC Code管理系统**：统一管理所有FC Code字段定义
3. **提示词映射服务**：为每个FC Code提供专用的LLM提示词
4. **模板版本控制**：支持模板的版本管理和更新机制
5. **质量控制系统**：确保生成内容的质量和合规性

## 2. 病历模板解析和处理流程

### 2.1 XML模板解析架构

#### 2.1.1 解析流程设计
```mermaid
flowchart TD
    A[XML模板输入] --> B[XML结构验证]
    B --> C[FC Code字段提取]
    C --> D[字段属性解析]
    D --> E[模板元数据生成]
    E --> F[数据库存储]
    F --> G[缓存更新]
    
    H[FC Code映射表] --> C
    I[字段验证规则] --> D
    J[模板配置] --> E
```

#### 2.1.2 核心解析器设计
```python
class MedicalTemplateParser:
    """医疗模板解析器"""
    
    def __init__(self, fc_code_manager: FCCodeManager):
        self.fc_code_manager = fc_code_manager
        self.xml_parser = XMLParser()
        self.field_extractor = FieldExtractor()
    
    def parse_template(self, xml_content: str, template_metadata: dict) -> TemplateParseResult:
        """
        解析医疗模板
        
        Args:
            xml_content: XML模板内容
            template_metadata: 模板元数据
        
        Returns:
            TemplateParseResult: 解析结果
        """
        # 1. XML结构验证
        validation_result = self.xml_parser.validate_structure(xml_content)
        if not validation_result.is_valid:
            raise TemplateValidationError(validation_result.errors)
        
        # 2. 解析XML文档
        xml_doc = self.xml_parser.parse(xml_content)
        
        # 3. 提取FC Code字段
        fc_fields = self.field_extractor.extract_fc_code_fields(xml_doc)
        
        # 4. 字段属性解析
        parsed_fields = []
        for field in fc_fields:
            field_info = self._parse_field_attributes(field)
            field_info.update(self._get_fc_code_definition(field.fc_code))
            parsed_fields.append(field_info)
        
        # 5. 生成模板定义
        template_definition = TemplateDefinition(
            template_id=template_metadata['template_id'],
            template_name=template_metadata['template_name'],
            record_type=template_metadata['record_type'],
            fields=parsed_fields,
            xml_content=xml_content,
            metadata=template_metadata
        )
        
        return TemplateParseResult(
            template_definition=template_definition,
            validation_result=validation_result,
            field_count=len(parsed_fields),
            llm_field_count=len([f for f in parsed_fields if f.is_llm_generated])
        )
    
    def _parse_field_attributes(self, field_element: ET.Element) -> dict:
        """解析字段属性"""
        attributes = {}
        
        # 解析基本属性
        attributes['fc_code'] = self._extract_fc_code(field_element)
        attributes['field_name'] = self._extract_field_name(field_element)
        attributes['field_type'] = self._extract_field_type(field_element)
        attributes['is_editable'] = self._extract_editability(field_element)
        attributes['current_value'] = self._extract_current_value(field_element)
        attributes['background_text'] = self._extract_background_text(field_element)
        
        # 解析显示属性
        attributes['display_order'] = self._extract_display_order(field_element)
        attributes['element_type'] = field_element.tag.split('}')[-1] if '}' in field_element.tag else field_element.tag
        
        return attributes
    
    def _get_fc_code_definition(self, fc_code: str) -> dict:
        """获取FC Code定义信息"""
        return self.fc_code_manager.get_fc_code_definition(fc_code)
```

### 2.2 字段提取和分类

#### 2.2.1 FC Code字段提取策略
```python
class FieldExtractor:
    """字段提取器"""
    
    def extract_fc_code_fields(self, xml_doc: ET.Element) -> List[FCCodeField]:
        """提取所有FC Code字段"""
        fields = []
        
        # 策略1: 从Attributes中提取
        fields.extend(self._extract_from_attributes(xml_doc))
        
        # 策略2: 从Name属性中提取
        fields.extend(self._extract_from_name_attributes(xml_doc))
        
        # 策略3: 从ID属性中提取
        fields.extend(self._extract_from_id_attributes(xml_doc))
        
        # 去重和验证
        unique_fields = self._deduplicate_fields(fields)
        validated_fields = self._validate_fields(unique_fields)
        
        return validated_fields
    
    def _extract_from_attributes(self, xml_doc: ET.Element) -> List[FCCodeField]:
        """从Attributes中提取FC Code字段"""
        fields = []
        
        for element in xml_doc.iter():
            if element.tag.endswith('InputField'):
                attributes = element.find('.//Attributes')
                if attributes is not None:
                    fc_code = self._find_attribute_value(attributes, 'FieldCode')
                    if fc_code and self._is_valid_fc_code(fc_code):
                        field = FCCodeField(
                            fc_code=fc_code,
                            xml_element=element,
                            extraction_method='attributes'
                        )
                        fields.append(field)
        
        return fields
    
    def _is_valid_fc_code(self, code: str) -> bool:
        """验证FC Code格式"""
        import re
        return bool(re.match(r'^FC\d{10}$', code))
```

#### 2.2.2 字段分类和优先级
```python
class FCCodeClassifier:
    """FC Code分类器"""
    
    CLASSIFICATION_RULES = {
        'patient_info': {
            'pattern': r'^FC000000(1099|1108|1105|1092|1114|1104)$',
            'priority': 1,
            'is_llm_generated': False,
            'data_source': 'patient_data'
        },
        'medical_content': {
            'pattern': r'^FC000000(1144|1113|0850|0854|0855)$',
            'priority': 5,
            'is_llm_generated': True,
            'data_source': 'llm_generation'
        },
        'system_info': {
            'pattern': r'^FC000001(5114|5208|5219)$',
            'priority': 2,
            'is_llm_generated': False,
            'data_source': 'system_config'
        },
        'signature': {
            'pattern': r'^FC000000(1115|1107|1159|1147)$',
            'priority': 3,
            'is_llm_generated': False,
            'data_source': 'user_input'
        },
        'datetime': {
            'pattern': r'^FC000000(1100|1145)$',
            'priority': 2,
            'is_llm_generated': False,
            'data_source': 'system_time'
        }
    }
    
    def classify_fc_code(self, fc_code: str) -> dict:
        """分类FC Code"""
        import re
        
        for category, rules in self.CLASSIFICATION_RULES.items():
            if re.match(rules['pattern'], fc_code):
                return {
                    'category': category,
                    'priority': rules['priority'],
                    'is_llm_generated': rules['is_llm_generated'],
                    'data_source': rules['data_source']
                }
        
        # 默认分类
        return {
            'category': 'unknown',
            'priority': 1,
            'is_llm_generated': False,
            'data_source': 'manual_input'
        }
```

## 3. FC Code管理机制

### 3.1 FC Code统一管理架构

#### 3.1.1 管理器设计
```python
class FCCodeManager:
    """FC Code统一管理器"""
    
    def __init__(self, database_connection, cache_manager):
        self.db = database_connection
        self.cache = cache_manager
        self.classifier = FCCodeClassifier()
        self.validator = FCCodeValidator()
    
    def register_fc_code(self, fc_code_info: FCCodeInfo) -> bool:
        """注册新的FC Code"""
        # 1. 验证FC Code格式
        if not self.validator.validate_format(fc_code_info.fc_code):
            raise InvalidFCCodeError(f"Invalid FC Code format: {fc_code_info.fc_code}")
        
        # 2. 检查是否已存在
        if self.exists(fc_code_info.fc_code, fc_code_info.record_type):
            raise FCCodeExistsError(f"FC Code already exists: {fc_code_info.fc_code}")
        
        # 3. 自动分类
        classification = self.classifier.classify_fc_code(fc_code_info.fc_code)
        fc_code_info.update(classification)
        
        # 4. 存储到数据库
        result = self._store_fc_code(fc_code_info)
        
        # 5. 更新缓存
        if result:
            self.cache.invalidate_fc_code_cache(fc_code_info.fc_code)
        
        return result
    
    def get_fc_code_definition(self, fc_code: str, record_type: str = None) -> FCCodeDefinition:
        """获取FC Code定义"""
        cache_key = f"fc_code:{fc_code}:{record_type or 'all'}"
        
        # 尝试从缓存获取
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return FCCodeDefinition.from_dict(cached_result)
        
        # 从数据库查询
        query = """
        SELECT fc_code, field_name, field_content_requirements,
               field_format_specification, llm_prompt_template,
               is_required, is_llm_generated, validation_rules,
               quality_requirements, priority_level
        FROM fc_code_templates 
        WHERE fc_code = %s AND status = 'active'
        """
        params = [fc_code]
        
        if record_type:
            query += " AND record_type = %s"
            params.append(record_type)
        
        query += " ORDER BY priority_level DESC LIMIT 1"
        
        result = self.db.execute(query, params).fetchone()
        
        if not result:
            raise FCCodeNotFoundError(f"FC Code not found: {fc_code}")
        
        definition = FCCodeDefinition.from_db_result(result)
        
        # 缓存结果
        self.cache.set(cache_key, definition.to_dict(), ttl=3600)
        
        return definition
    
    def update_prompt_template(self, fc_code: str, record_type: str, 
                              new_template: str, version: str) -> bool:
        """更新提示词模板"""
        # 1. 验证模板格式
        if not self.validator.validate_prompt_template(new_template):
            raise InvalidPromptTemplateError("Invalid prompt template format")
        
        # 2. 创建新版本记录
        update_data = {
            'llm_prompt_template': new_template,
            'prompt_version': version,
            'updated_at': datetime.now(),
            'updated_by': self._get_current_user()
        }
        
        # 3. 更新数据库
        query = """
        UPDATE fc_code_templates 
        SET llm_prompt_template = %s, prompt_version = %s, 
            updated_at = %s, updated_by = %s
        WHERE fc_code = %s AND record_type = %s AND status = 'active'
        """
        
        result = self.db.execute(query, [
            new_template, version, update_data['updated_at'], 
            update_data['updated_by'], fc_code, record_type
        ])
        
        # 4. 清除缓存
        if result.rowcount > 0:
            self.cache.invalidate_fc_code_cache(fc_code)
            return True
        
        return False
```

### 3.2 FC Code版本管理

#### 3.2.1 版本控制策略
```python
class FCCodeVersionManager:
    """FC Code版本管理器"""
    
    def __init__(self, database_connection):
        self.db = database_connection
    
    def create_version(self, fc_code: str, record_type: str, 
                      changes: dict, version_type: str = 'minor') -> str:
        """创建新版本"""
        # 1. 获取当前版本
        current_version = self._get_current_version(fc_code, record_type)
        
        # 2. 计算新版本号
        new_version = self._calculate_new_version(current_version, version_type)
        
        # 3. 创建版本记录
        version_record = {
            'fc_code': fc_code,
            'record_type': record_type,
            'version': new_version,
            'changes': changes,
            'change_type': version_type,
            'created_at': datetime.now(),
            'created_by': self._get_current_user()
        }
        
        # 4. 存储版本历史
        self._store_version_history(version_record)
        
        return new_version
    
    def rollback_version(self, fc_code: str, record_type: str, 
                        target_version: str) -> bool:
        """回滚到指定版本"""
        # 1. 获取目标版本数据
        target_data = self._get_version_data(fc_code, record_type, target_version)
        
        if not target_data:
            raise VersionNotFoundError(f"Version {target_version} not found")
        
        # 2. 更新当前记录
        update_query = """
        UPDATE fc_code_templates 
        SET llm_prompt_template = %s, prompt_version = %s,
            field_content_requirements = %s, field_format_specification = %s,
            updated_at = %s, updated_by = %s
        WHERE fc_code = %s AND record_type = %s AND status = 'active'
        """
        
        result = self.db.execute(update_query, [
            target_data['llm_prompt_template'],
            target_version,
            target_data['field_content_requirements'],
            target_data['field_format_specification'],
            datetime.now(),
            self._get_current_user(),
            fc_code,
            record_type
        ])
        
        return result.rowcount > 0
    
    def get_version_history(self, fc_code: str, record_type: str) -> List[dict]:
        """获取版本历史"""
        query = """
        SELECT version, changes, change_type, created_at, created_by
        FROM fc_code_version_history 
        WHERE fc_code = %s AND record_type = %s
        ORDER BY created_at DESC
        """
        
        results = self.db.execute(query, [fc_code, record_type]).fetchall()
        return [dict(row) for row in results]
```

## 4. LLM提示词精确映射和调用

### 4.1 提示词映射服务

#### 4.1.1 映射服务架构
```python
class PromptMappingService:
    """提示词映射服务"""
    
    def __init__(self, fc_code_manager: FCCodeManager, 
                 context_builder: ContextBuilder):
        self.fc_code_manager = fc_code_manager
        self.context_builder = context_builder
        self.template_engine = TemplateEngine()
    
    def get_prompt_for_fc_code(self, fc_code: str, record_type: str, 
                              context: dict) -> PromptInfo:
        """获取FC Code对应的提示词"""
        # 1. 获取FC Code定义
        fc_definition = self.fc_code_manager.get_fc_code_definition(fc_code, record_type)
        
        # 2. 构建上下文
        full_context = self.context_builder.build_context(context, fc_definition)
        
        # 3. 渲染提示词模板
        rendered_prompt = self.template_engine.render(
            fc_definition.llm_prompt_template, 
            full_context
        )
        
        # 4. 构建提示词信息
        prompt_info = PromptInfo(
            fc_code=fc_code,
            record_type=record_type,
            prompt_text=rendered_prompt,
            context=full_context,
            requirements=fc_definition.field_content_requirements,
            format_spec=fc_definition.field_format_specification,
            validation_rules=fc_definition.validation_rules,
            quality_requirements=fc_definition.quality_requirements
        )
        
        return prompt_info
    
    def batch_get_prompts(self, fc_codes: List[str], record_type: str, 
                         context: dict) -> Dict[str, PromptInfo]:
        """批量获取多个FC Code的提示词"""
        prompts = {}
        
        for fc_code in fc_codes:
            try:
                prompt_info = self.get_prompt_for_fc_code(fc_code, record_type, context)
                prompts[fc_code] = prompt_info
            except Exception as e:
                # 记录错误但继续处理其他FC Code
                logger.error(f"Failed to get prompt for {fc_code}: {e}")
                prompts[fc_code] = None
        
        return prompts
```

### 4.2 LLM调用和内容生成

#### 4.2.1 内容生成服务
```python
class ContentGenerationService:
    """内容生成服务"""

    def __init__(self, llm_client: LLMClient,
                 prompt_mapping_service: PromptMappingService,
                 quality_assessor: QualityAssessor):
        self.llm_client = llm_client
        self.prompt_service = prompt_mapping_service
        self.quality_assessor = quality_assessor

    async def generate_fc_code_content(self, fc_code: str, record_type: str,
                                      context: dict) -> GenerationResult:
        """为指定FC Code生成内容"""
        # 1. 获取提示词
        prompt_info = self.prompt_service.get_prompt_for_fc_code(
            fc_code, record_type, context
        )

        # 2. 调用LLM生成内容
        generation_start = time.time()
        generated_content = await self.llm_client.generate(
            prompt=prompt_info.prompt_text,
            max_tokens=self._calculate_max_tokens(prompt_info),
            temperature=0.7,
            stop_sequences=None
        )
        generation_time = time.time() - generation_start

        # 3. 质量评估
        quality_result = self.quality_assessor.assess_content(
            content=generated_content,
            requirements=prompt_info.requirements,
            validation_rules=prompt_info.validation_rules
        )

        # 4. 内容验证
        validation_result = self._validate_content(
            generated_content, prompt_info
        )

        # 5. 构建结果
        result = GenerationResult(
            fc_code=fc_code,
            record_type=record_type,
            generated_content=generated_content,
            generation_time=generation_time,
            quality_score=quality_result.overall_score,
            quality_details=quality_result.details,
            validation_result=validation_result,
            prompt_used=prompt_info.prompt_text,
            context_used=context
        )

        return result

    async def batch_generate_content(self, fc_codes: List[str], record_type: str,
                                   context: dict) -> Dict[str, GenerationResult]:
        """批量生成多个FC Code的内容"""
        results = {}

        # 获取所有提示词
        prompts = self.prompt_service.batch_get_prompts(fc_codes, record_type, context)

        # 并发生成内容
        tasks = []
        for fc_code, prompt_info in prompts.items():
            if prompt_info:
                task = self._generate_single_content(fc_code, prompt_info, context)
                tasks.append(task)

        # 等待所有任务完成
        completed_results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for i, result in enumerate(completed_results):
            fc_code = list(prompts.keys())[i]
            if isinstance(result, Exception):
                logger.error(f"Generation failed for {fc_code}: {result}")
                results[fc_code] = GenerationResult.create_error_result(fc_code, str(result))
            else:
                results[fc_code] = result

        return results
```

#### 4.2.2 质量控制和验证
```python
class QualityAssessor:
    """质量评估器"""

    def assess_content(self, content: str, requirements: str,
                      validation_rules: dict) -> QualityAssessmentResult:
        """评估内容质量"""
        scores = {}
        details = {}

        # 1. 长度检查
        length_score, length_details = self._assess_length(content, validation_rules)
        scores['length'] = length_score
        details['length'] = length_details

        # 2. 完整性检查
        completeness_score, completeness_details = self._assess_completeness(
            content, validation_rules.get('required_elements', [])
        )
        scores['completeness'] = completeness_score
        details['completeness'] = completeness_details

        # 3. 医学术语检查
        terminology_score, terminology_details = self._assess_medical_terminology(content)
        scores['terminology'] = terminology_score
        details['terminology'] = terminology_details

        # 4. 结构化程度检查
        structure_score, structure_details = self._assess_structure(content, validation_rules)
        scores['structure'] = structure_score
        details['structure'] = structure_details

        # 5. 计算综合评分
        weights = {
            'length': 0.2,
            'completeness': 0.4,
            'terminology': 0.3,
            'structure': 0.1
        }

        overall_score = sum(scores[key] * weights[key] for key in scores)

        return QualityAssessmentResult(
            overall_score=overall_score,
            detailed_scores=scores,
            details=details,
            is_acceptable=overall_score >= 75,  # 75分及格线
            recommendations=self._generate_recommendations(scores, details)
        )
```

## 5. 模板版本管理和更新机制

### 5.1 版本管理策略

#### 5.1.1 版本控制数据模型
```sql
-- FC Code版本历史表
CREATE TABLE fc_code_version_history (
    id BIGSERIAL PRIMARY KEY,
    fc_code VARCHAR(20) NOT NULL,
    record_type VARCHAR(50) NOT NULL,
    version VARCHAR(20) NOT NULL,

    -- 版本内容
    llm_prompt_template TEXT NOT NULL,
    field_content_requirements TEXT,
    field_format_specification TEXT,
    validation_rules JSONB,
    quality_requirements JSONB,

    -- 变更信息
    change_type VARCHAR(20) NOT NULL,  -- major/minor/patch
    change_description TEXT,
    changes JSONB,  -- 具体变更内容

    -- 版本状态
    status VARCHAR(20) DEFAULT 'active',  -- active/deprecated/rollback

    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),

    -- 索引
    INDEX idx_fc_version_code_type (fc_code, record_type),
    INDEX idx_fc_version_created_at (created_at)
);

-- 模板更新日志表
CREATE TABLE template_update_logs (
    id BIGSERIAL PRIMARY KEY,
    template_id VARCHAR(100) NOT NULL,
    update_type VARCHAR(50) NOT NULL,  -- fc_code_add/fc_code_remove/prompt_update

    -- 更新内容
    affected_fc_codes JSONB,
    old_values JSONB,
    new_values JSONB,

    -- 更新结果
    update_status VARCHAR(20) NOT NULL,  -- success/failed/partial
    error_message TEXT,
    affected_records INTEGER DEFAULT 0,

    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),

    -- 索引
    INDEX idx_template_update_template_id (template_id),
    INDEX idx_template_update_type (update_type),
    INDEX idx_template_update_status (update_status)
);
```

#### 5.1.2 自动更新机制
```python
class TemplateUpdateManager:
    """模板更新管理器"""

    def __init__(self, database_connection, notification_service):
        self.db = database_connection
        self.notification = notification_service
        self.version_manager = FCCodeVersionManager(database_connection)

    def update_template_from_xml(self, template_id: str, new_xml_content: str,
                                update_metadata: dict) -> UpdateResult:
        """从新的XML内容更新模板"""
        # 1. 解析新的XML内容
        parser = MedicalTemplateParser(FCCodeManager(self.db, None))
        new_template = parser.parse_template(new_xml_content, update_metadata)

        # 2. 获取当前模板定义
        current_template = self._get_current_template(template_id)

        # 3. 比较差异
        diff_result = self._compare_templates(current_template, new_template)

        # 4. 执行更新
        update_result = self._execute_template_update(diff_result, update_metadata)

        # 5. 记录更新日志
        self._log_template_update(template_id, diff_result, update_result)

        # 6. 发送通知
        if update_result.has_changes:
            self.notification.send_template_update_notification(
                template_id, diff_result, update_result
            )

        return update_result

    def _compare_templates(self, current: TemplateDefinition,
                          new: TemplateDefinition) -> TemplateDiffResult:
        """比较模板差异"""
        diff = TemplateDiffResult()

        # 比较FC Code字段
        current_fc_codes = {f.fc_code for f in current.fields}
        new_fc_codes = {f.fc_code for f in new.fields}

        diff.added_fc_codes = new_fc_codes - current_fc_codes
        diff.removed_fc_codes = current_fc_codes - new_fc_codes
        diff.common_fc_codes = current_fc_codes & new_fc_codes

        # 比较字段属性变化
        for fc_code in diff.common_fc_codes:
            current_field = current.get_field(fc_code)
            new_field = new.get_field(fc_code)

            field_diff = self._compare_field_attributes(current_field, new_field)
            if field_diff.has_changes:
                diff.modified_fields[fc_code] = field_diff

        return diff

    def schedule_batch_update(self, update_plan: BatchUpdatePlan) -> str:
        """调度批量更新"""
        # 1. 创建更新任务
        task_id = self._create_update_task(update_plan)

        # 2. 验证更新计划
        validation_result = self._validate_update_plan(update_plan)
        if not validation_result.is_valid:
            raise UpdatePlanValidationError(validation_result.errors)

        # 3. 执行更新
        asyncio.create_task(self._execute_batch_update(task_id, update_plan))

        return task_id

    async def _execute_batch_update(self, task_id: str,
                                   update_plan: BatchUpdatePlan):
        """执行批量更新"""
        try:
            # 更新任务状态
            self._update_task_status(task_id, 'running')

            results = []
            for update_item in update_plan.items:
                try:
                    result = await self._execute_single_update(update_item)
                    results.append(result)
                except Exception as e:
                    logger.error(f"Update failed for {update_item.template_id}: {e}")
                    results.append(UpdateResult.create_error_result(update_item, str(e)))

            # 汇总结果
            summary = self._summarize_batch_results(results)

            # 更新任务状态
            self._update_task_status(task_id, 'completed', summary)

            # 发送完成通知
            self.notification.send_batch_update_completion(task_id, summary)

        except Exception as e:
            logger.error(f"Batch update failed: {e}")
            self._update_task_status(task_id, 'failed', {'error': str(e)})
```

## 6. 系统集成和API接口

### 6.1 RESTful API设计

#### 6.1.1 FC Code管理API
```python
from fastapi import FastAPI, HTTPException, Depends
from typing import List, Optional

app = FastAPI(title="FC Code Management API")

@app.get("/api/v1/fc-codes/{fc_code}")
async def get_fc_code_definition(
    fc_code: str,
    record_type: Optional[str] = None,
    fc_manager: FCCodeManager = Depends(get_fc_manager)
) -> FCCodeDefinitionResponse:
    """获取FC Code定义"""
    try:
        definition = fc_manager.get_fc_code_definition(fc_code, record_type)
        return FCCodeDefinitionResponse.from_definition(definition)
    except FCCodeNotFoundError:
        raise HTTPException(status_code=404, detail="FC Code not found")

@app.post("/api/v1/fc-codes")
async def create_fc_code(
    fc_code_info: FCCodeCreateRequest,
    fc_manager: FCCodeManager = Depends(get_fc_manager)
) -> FCCodeCreateResponse:
    """创建新的FC Code"""
    try:
        result = fc_manager.register_fc_code(fc_code_info.to_fc_code_info())
        return FCCodeCreateResponse(success=result, fc_code=fc_code_info.fc_code)
    except (InvalidFCCodeError, FCCodeExistsError) as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.put("/api/v1/fc-codes/{fc_code}/prompt")
async def update_prompt_template(
    fc_code: str,
    record_type: str,
    prompt_update: PromptUpdateRequest,
    fc_manager: FCCodeManager = Depends(get_fc_manager)
) -> PromptUpdateResponse:
    """更新提示词模板"""
    try:
        result = fc_manager.update_prompt_template(
            fc_code, record_type,
            prompt_update.template, prompt_update.version
        )
        return PromptUpdateResponse(success=result, version=prompt_update.version)
    except InvalidPromptTemplateError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/api/v1/fc-codes/{fc_code}/generate")
async def generate_content(
    fc_code: str,
    generation_request: ContentGenerationRequest,
    content_service: ContentGenerationService = Depends(get_content_service)
) -> ContentGenerationResponse:
    """生成FC Code内容"""
    try:
        result = await content_service.generate_fc_code_content(
            fc_code,
            generation_request.record_type,
            generation_request.context
        )
        return ContentGenerationResponse.from_generation_result(result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

#### 6.1.2 模板管理API
```python
@app.post("/api/v1/templates/parse")
async def parse_template(
    template_request: TemplateParseRequest,
    parser: MedicalTemplateParser = Depends(get_template_parser)
) -> TemplateParseResponse:
    """解析医疗模板"""
    try:
        result = parser.parse_template(
            template_request.xml_content,
            template_request.metadata
        )
        return TemplateParseResponse.from_parse_result(result)
    except TemplateValidationError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.put("/api/v1/templates/{template_id}")
async def update_template(
    template_id: str,
    update_request: TemplateUpdateRequest,
    update_manager: TemplateUpdateManager = Depends(get_update_manager)
) -> TemplateUpdateResponse:
    """更新模板"""
    try:
        result = update_manager.update_template_from_xml(
            template_id,
            update_request.xml_content,
            update_request.metadata
        )
        return TemplateUpdateResponse.from_update_result(result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/templates/{template_id}/fc-codes")
async def get_template_fc_codes(
    template_id: str,
    record_type: Optional[str] = None,
    fc_manager: FCCodeManager = Depends(get_fc_manager)
) -> TemplateFCCodesResponse:
    """获取模板的FC Code列表"""
    fc_codes = fc_manager.get_template_fc_codes(template_id, record_type)
    return TemplateFCCodesResponse(
        template_id=template_id,
        record_type=record_type,
        fc_codes=fc_codes
    )
```

### 6.2 事件驱动架构

#### 6.2.1 事件定义
```python
from dataclasses import dataclass
from typing import Any, Dict
from datetime import datetime

@dataclass
class FCCodeEvent:
    """FC Code事件基类"""
    event_type: str
    fc_code: str
    record_type: str
    timestamp: datetime
    metadata: Dict[str, Any]

@dataclass
class FCCodeCreatedEvent(FCCodeEvent):
    """FC Code创建事件"""
    event_type: str = "fc_code_created"

@dataclass
class PromptTemplateUpdatedEvent(FCCodeEvent):
    """提示词模板更新事件"""
    event_type: str = "prompt_template_updated"
    old_version: str
    new_version: str

@dataclass
class ContentGeneratedEvent(FCCodeEvent):
    """内容生成事件"""
    event_type: str = "content_generated"
    generation_time: float
    quality_score: float
    content_length: int

class EventPublisher:
    """事件发布器"""

    def __init__(self, event_bus):
        self.event_bus = event_bus

    def publish_fc_code_created(self, fc_code: str, record_type: str,
                               metadata: dict):
        """发布FC Code创建事件"""
        event = FCCodeCreatedEvent(
            fc_code=fc_code,
            record_type=record_type,
            timestamp=datetime.now(),
            metadata=metadata
        )
        self.event_bus.publish(event)

    def publish_prompt_updated(self, fc_code: str, record_type: str,
                              old_version: str, new_version: str):
        """发布提示词更新事件"""
        event = PromptTemplateUpdatedEvent(
            fc_code=fc_code,
            record_type=record_type,
            old_version=old_version,
            new_version=new_version,
            timestamp=datetime.now(),
            metadata={}
        )
        self.event_bus.publish(event)
```

## 7. 监控和运维

### 7.1 系统监控指标

#### 7.1.1 关键性能指标
```python
class FCCodeMetricsCollector:
    """FC Code指标收集器"""

    def __init__(self, metrics_client):
        self.metrics = metrics_client

    def record_content_generation(self, fc_code: str, record_type: str,
                                 generation_time: float, quality_score: float):
        """记录内容生成指标"""
        self.metrics.histogram(
            'fc_code_generation_time',
            generation_time,
            tags={'fc_code': fc_code, 'record_type': record_type}
        )

        self.metrics.gauge(
            'fc_code_quality_score',
            quality_score,
            tags={'fc_code': fc_code, 'record_type': record_type}
        )

        self.metrics.counter(
            'fc_code_generation_count',
            tags={'fc_code': fc_code, 'record_type': record_type}
        )

    def record_template_parse(self, template_id: str, parse_time: float,
                             field_count: int, success: bool):
        """记录模板解析指标"""
        self.metrics.histogram(
            'template_parse_time',
            parse_time,
            tags={'template_id': template_id}
        )

        self.metrics.gauge(
            'template_field_count',
            field_count,
            tags={'template_id': template_id}
        )

        self.metrics.counter(
            'template_parse_count',
            tags={'template_id': template_id, 'success': str(success)}
        )
```

#### 7.1.2 告警规则
```yaml
# Prometheus告警规则
groups:
  - name: fc_code_alerts
    rules:
      - alert: HighGenerationLatency
        expr: histogram_quantile(0.95, fc_code_generation_time) > 30
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "FC Code内容生成延迟过高"
          description: "95%的FC Code内容生成时间超过30秒"

      - alert: LowQualityScore
        expr: avg_over_time(fc_code_quality_score[10m]) < 75
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "FC Code内容质量评分过低"
          description: "最近10分钟平均质量评分低于75分"

      - alert: TemplateParseFailure
        expr: rate(template_parse_count{success="false"}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "模板解析失败率过高"
          description: "模板解析失败率超过10%"
```

---

**文档版本**: v1.0
**创建日期**: 2025-08-19
**最后更新**: 2025-08-19
**负责人**: 开发团队
**相关文档**: 《医疗病历自动化处理系统需求文档》
