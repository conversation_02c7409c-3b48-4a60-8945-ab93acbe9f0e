-- FC Code模板信息数据库表设计
-- 用于存储病程记录模板的FC Code信息和LLM提示词

-- 创建FC Code模板信息表
CREATE TABLE fc_code_templates (
    id BIGSERIAL PRIMARY KEY,
    
    -- FC Code基本信息
    fc_code VARCHAR(20) NOT NULL,                          -- FC Code标识符 (如: FC0000001144)
    field_name VARCHAR(200) NOT NULL,                      -- 字段名称 (如: 病程记录内容)
    field_description TEXT,                                -- 字段描述
    
    -- 内容要求和格式规范
    field_content_requirements TEXT NOT NULL,              -- 字段内容要求
    field_format_specification TEXT NOT NULL,              -- 字段格式规范
    min_length INTEGER DEFAULT 0,                          -- 最小字符长度
    max_length INTEGER DEFAULT 10000,                      -- 最大字符长度
    
    -- LLM提示词配置
    llm_prompt_template TEXT NOT NULL,                     -- 给LLM的专用提示词模板
    prompt_version VARCHAR(20) DEFAULT '1.0',              -- 提示词版本
    prompt_language VARCHAR(10) DEFAULT 'zh-CN',           -- 提示词语言
    
    -- 模板关联信息
    template_id VARCHAR(100) NOT NULL,                     -- 对应的病历模板ID
    template_name VARCHAR(200) NOT NULL,                   -- 病历模板名称
    record_type VARCHAR(50) NOT NULL,                      -- 病程记录类型
    
    -- 字段属性
    is_required BOOLEAN DEFAULT FALSE,                     -- 是否必填字段
    is_editable BOOLEAN DEFAULT TRUE,                      -- 是否可编辑
    is_llm_generated BOOLEAN DEFAULT FALSE,                -- 是否需要LLM生成
    field_data_type VARCHAR(20) DEFAULT 'S1',              -- 字段数据类型 (S1=字符串, N=数字, DT=日期时间)
    
    -- 验证和质量控制
    validation_rules JSONB,                                -- 验证规则 (JSON格式)
    quality_requirements JSONB,                            -- 质量要求 (JSON格式)
    
    -- 优先级和排序
    display_order INTEGER DEFAULT 0,                       -- 显示顺序
    priority_level INTEGER DEFAULT 1,                      -- 优先级 (1-5, 5最高)
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'active',                   -- 状态 (active/inactive/deprecated)
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),                               -- 创建人
    updated_by VARCHAR(100),                               -- 更新人
    
    -- 约束和索引
    CONSTRAINT uk_fc_code_template UNIQUE (fc_code, template_id, record_type),
    
    -- 检查约束
    CONSTRAINT chk_record_type CHECK (record_type IN (
        'first_progress', 'chief_ward_round', 'attending_ward_round', 'daily_progress'
    )),
    CONSTRAINT chk_status CHECK (status IN ('active', 'inactive', 'deprecated')),
    CONSTRAINT chk_priority_level CHECK (priority_level BETWEEN 1 AND 5),
    CONSTRAINT chk_length_constraints CHECK (min_length >= 0 AND max_length > min_length)
);

-- 创建索引
CREATE INDEX idx_fc_code_templates_fc_code ON fc_code_templates(fc_code);
CREATE INDEX idx_fc_code_templates_template_id ON fc_code_templates(template_id);
CREATE INDEX idx_fc_code_templates_record_type ON fc_code_templates(record_type);
CREATE INDEX idx_fc_code_templates_status ON fc_code_templates(status);
CREATE INDEX idx_fc_code_templates_llm_generated ON fc_code_templates(is_llm_generated);
CREATE INDEX idx_fc_code_templates_required ON fc_code_templates(is_required);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_fc_code_templates_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_fc_code_templates_updated_at
    BEFORE UPDATE ON fc_code_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_fc_code_templates_updated_at();

-- 插入初始数据 - 首次病程记录
INSERT INTO fc_code_templates (
    fc_code, field_name, field_content_requirements, field_format_specification,
    llm_prompt_template, template_id, template_name, record_type,
    is_required, is_llm_generated, min_length, max_length, display_order, priority_level,
    validation_rules, quality_requirements
) VALUES 
-- 首次病程记录 - 病程记录内容
(
    'FC0000001144',
    '病程记录内容',
    '详细描述患者病情、入院诊断、治疗计划和观察要点。必须包含：1.病情评估 2.诊断分析 3.治疗计划 4.观察要点',
    '300-500字，结构化医疗记录，使用规范医学术语',
    '你是一名经验丰富的临床医生，正在为患者编写首次病程记录。

患者基本信息：
- 姓名：{patient_name}，{gender}，{age}岁
- 住院号：{admission_no}
- 科室：{department}
- 入院诊断：{admission_diagnosis}
- 入院时间：{admission_time}

医生输入的关键信息：
{doctor_inputs}

请按照以下结构生成首次病程记录内容：

1. 病情评估（100-150字）：
   - 患者当前主要症状和体征
   - 生命体征和一般情况
   - 重要检查结果

2. 诊断分析（50-100字）：
   - 确认入院诊断的依据
   - 需要鉴别的疾病

3. 治疗计划（100-150字）：
   - 具体的治疗措施
   - 用药方案
   - 检查安排

4. 观察要点（50-100字）：
   - 需要密切观察的指标
   - 可能出现的并发症

要求：
- 总字数控制在300-500字
- 使用规范的医学术语
- 逻辑清晰，条理分明
- 符合首次病程记录的医疗规范

请生成病程记录内容：',
    'TEMPLATE_FIRST_PROGRESS_001',
    '首次病程记录模板',
    'first_progress',
    true,
    true,
    300,
    500,
    1,
    5,
    '{"required_elements": ["病情评估", "诊断分析", "治疗计划", "观察要点"], "medical_terms_required": true, "structure_required": true}',
    '{"min_score": 85, "required_completeness": 100, "terminology_accuracy": 90}'
),

-- 主治医师查房记录 - 查房记录内容
(
    'FC0000001144',
    '查房记录内容',
    '记录主治医师查房时的病情评估、治疗效果分析和下一步计划。必须包含：1.病情评估 2.治疗效果 3.下一步计划',
    '200-400字，结构化查房记录，体现主治医师专业判断',
    '你是一名主治医师，正在进行查房并记录查房内容。

患者信息：
- 姓名：{patient_name}，{gender}，{age}岁
- 住院天数：{days_hospitalized}天
- 当前诊断：{current_diagnosis}

病情变化：
{condition_changes}

治疗情况：
{current_treatment}

最新检查结果：
{recent_examinations}

请按照以下结构生成主治医师查房记录：

1. 病情评估（80-120字）：
   - 患者当前病情状态
   - 与前次查房的变化对比
   - 治疗反应情况

2. 治疗效果分析（60-100字）：
   - 现有治疗方案的效果
   - 存在的问题和不足

3. 下一步计划（60-120字）：
   - 治疗方案的调整
   - 新的检查安排
   - 预期目标

要求：
- 总字数控制在200-400字
- 体现主治医师的专业水平
- 重点突出，针对性强
- 符合查房记录规范

请生成查房记录内容：',
    'TEMPLATE_ATTENDING_ROUND_001',
    '主治医师查房记录模板',
    'attending_ward_round',
    true,
    true,
    200,
    400,
    1,
    4,
    '{"required_elements": ["病情评估", "治疗效果", "下一步计划"], "professional_level": "attending", "comparison_required": true}',
    '{"min_score": 80, "required_completeness": 95, "professional_depth": 85}'
),

-- 主任医师查房记录 - 查房记录内容
(
    'FC0000001144',
    '查房记录内容',
    '记录主任医师查房时的高级医疗决策、疑难问题分析和指导意见。必须包含：1.病情综合分析 2.疑难问题解答 3.指导意见',
    '300-600字，高级查房记录，体现主任医师权威性和专业深度',
    '你是一名主任医师，正在进行高级查房并提供专业指导。

患者复杂情况：
- 姓名：{patient_name}，{gender}，{age}岁
- 复杂诊断：{complex_diagnosis}
- 治疗难点：{treatment_challenges}
- 会诊意见：{consultation_opinions}

疑难问题：
{difficult_issues}

下级医师汇报：
{junior_doctor_report}

请按照以下结构生成主任医师查房记录：

1. 病情综合分析（120-200字）：
   - 疾病的复杂性分析
   - 多系统问题的关联性
   - 预后评估

2. 疑难问题解答（100-200字）：
   - 诊断难点的分析
   - 治疗选择的依据
   - 风险效益评估

3. 指导意见（80-200字）：
   - 治疗方案的优化
   - 下级医师的培训指导
   - 质量控制要求

要求：
- 总字数控制在300-600字
- 体现主任医师的学术水平
- 具有指导和教学价值
- 符合高级查房记录规范

请生成主任医师查房记录内容：',
    'TEMPLATE_CHIEF_ROUND_001',
    '主任医师查房记录模板',
    'chief_ward_round',
    true,
    true,
    300,
    600,
    1,
    5,
    '{"required_elements": ["病情综合分析", "疑难问题解答", "指导意见"], "professional_level": "chief", "teaching_value": true}',
    '{"min_score": 90, "required_completeness": 100, "academic_depth": 95}'
),

-- 日常病程记录 - 病程记录内容
(
    'FC0000001144',
    '病程记录内容',
    '记录患者日常病情变化、治疗调整和观察结果。必须包含：1.病情变化 2.治疗执行 3.观察要点',
    '150-300字，简洁明了的日常记录，重点突出',
    '你是一名住院医师，正在记录患者的日常病程变化。

患者信息：
- 姓名：{patient_name}，{gender}，{age}岁
- 住院第{day_number}天
- 主要诊断：{main_diagnosis}

今日病情：
{today_condition}

治疗执行情况：
{treatment_execution}

观察结果：
{observation_results}

请按照以下结构生成日常病程记录：

1. 病情变化（60-100字）：
   - 症状的改善或加重
   - 体征的变化
   - 患者主观感受

2. 治疗执行（40-80字）：
   - 药物治疗的执行
   - 其他治疗措施的落实
   - 患者配合情况

3. 观察要点（50-120字）：
   - 重要指标的监测结果
   - 需要关注的问题
   - 下一步观察重点

要求：
- 总字数控制在150-300字
- 重点突出，避免冗余
- 客观记录，准确描述
- 符合日常病程记录规范

请生成日常病程记录内容：',
    'TEMPLATE_DAILY_PROGRESS_001',
    '日常病程记录模板',
    'daily_progress',
    true,
    true,
    150,
    300,
    1,
    3,
    '{"required_elements": ["病情变化", "治疗执行", "观察要点"], "professional_level": "resident", "concise_required": true}',
    '{"min_score": 75, "required_completeness": 90, "conciseness": 85}'
);

-- 插入非LLM生成的系统字段
INSERT INTO fc_code_templates (
    fc_code, field_name, field_content_requirements, field_format_specification,
    llm_prompt_template, template_id, template_name, record_type,
    is_required, is_llm_generated, display_order, priority_level
) VALUES 
-- 记录日期时间字段
('FC0000001100', '记录日期时间', '当前日期时间', 'yyyy年MM月dd日 HH时mm分', 
 'SYSTEM_GENERATED', 'ALL_TEMPLATES', '通用字段', 'first_progress', true, false, 2, 2),
('FC0000001145', '查房日期时间', '查房时间', 'yyyy年MM月dd日 HH时mm分', 
 'SYSTEM_GENERATED', 'ALL_TEMPLATES', '通用字段', 'attending_ward_round', true, false, 2, 2),
('FC0000001145', '查房日期时间', '查房时间', 'yyyy年MM月dd日 HH时mm分', 
 'SYSTEM_GENERATED', 'ALL_TEMPLATES', '通用字段', 'chief_ward_round', true, false, 2, 2),

-- 签名字段
('FC0000001147', '记录人签名', '记录医生姓名', '文本格式', 
 'USER_INPUT', 'ALL_TEMPLATES', '通用字段', 'first_progress', true, false, 3, 1),
('FC0000001159', '主治医师签名', '主治医师姓名', '文本格式', 
 'USER_INPUT', 'ALL_TEMPLATES', '通用字段', 'attending_ward_round', true, false, 3, 1),
('FC0000001107', '上级医师审签', '主任医师姓名', '文本格式', 
 'USER_INPUT', 'ALL_TEMPLATES', '通用字段', 'chief_ward_round', true, false, 3, 1),

-- 患者信息字段
('FC0000001099', '患者姓名', '患者真实姓名', '文本格式', 
 'PATIENT_DATA', 'ALL_TEMPLATES', '通用字段', 'first_progress', true, false, 4, 1),
('FC0000001108', '性别代码', '性别信息', '男/女', 
 'PATIENT_DATA', 'ALL_TEMPLATES', '通用字段', 'first_progress', true, false, 5, 1),
('FC0000001105', '年龄(岁)', '患者年龄', '数字格式', 
 'PATIENT_DATA', 'ALL_TEMPLATES', '通用字段', 'first_progress', true, false, 6, 1),
('FC0000001114', '住院号', '住院编号', '数字格式', 
 'PATIENT_DATA', 'ALL_TEMPLATES', '通用字段', 'first_progress', true, false, 7, 1);

-- 创建视图：获取LLM生成字段
CREATE VIEW v_llm_fc_code_templates AS
SELECT 
    fc_code,
    field_name,
    field_content_requirements,
    field_format_specification,
    llm_prompt_template,
    template_id,
    template_name,
    record_type,
    min_length,
    max_length,
    validation_rules,
    quality_requirements,
    priority_level
FROM fc_code_templates 
WHERE is_llm_generated = true 
  AND status = 'active'
ORDER BY record_type, priority_level DESC, display_order;

-- 创建视图：按记录类型分组的FC Code
CREATE VIEW v_fc_code_by_record_type AS
SELECT 
    record_type,
    COUNT(*) as total_fields,
    COUNT(CASE WHEN is_llm_generated THEN 1 END) as llm_fields,
    COUNT(CASE WHEN is_required THEN 1 END) as required_fields,
    STRING_AGG(CASE WHEN is_llm_generated THEN fc_code END, ', ') as llm_fc_codes
FROM fc_code_templates 
WHERE status = 'active'
GROUP BY record_type
ORDER BY record_type;

-- 添加表注释
COMMENT ON TABLE fc_code_templates IS '病程记录模板FC Code信息表，存储各类病程记录的字段定义和LLM提示词';
COMMENT ON COLUMN fc_code_templates.fc_code IS 'FC Code标识符，如FC0000001144';
COMMENT ON COLUMN fc_code_templates.llm_prompt_template IS 'LLM专用提示词模板，包含占位符用于动态替换';
COMMENT ON COLUMN fc_code_templates.validation_rules IS 'JSON格式的验证规则，包含必要元素、格式要求等';
COMMENT ON COLUMN fc_code_templates.quality_requirements IS 'JSON格式的质量要求，包含评分标准、完整性要求等';
