#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FC Code映射机制实际测试示例
基于真实抓包数据的完整演示

Author: 开发团队
Date: 2025-08-19
"""

import xml.etree.ElementTree as ET
from typing import Dict, Any
import json


def test_fc_code_mapping_with_real_data():
    """使用真实抓包数据测试FC Code映射"""
    
    # 基于抓包数据的真实XML片段
    real_xml_template = '''<?xml version="1.0" encoding="utf-8"?>
<XTextDocument xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <XElements>
        <!-- 患者姓名字段 -->
        <Element xsi:type="XInputField" StyleIndex="5">
            <Attributes>
                <Attribute>
                    <Name>FromType</Name>
                    <Value>emrfield</Value>
                </Attribute>
                <Attribute>
                    <Name>FieldName</Name>
                    <Value>患者姓名</Value>
                </Attribute>
                <Attribute>
                    <Name>FieldCode</Name>
                    <Value>FC0000001099</Value>
                </Attribute>
                <Attribute>
                    <Name>FieldDataType</Name>
                    <Value>S1</Value>
                </Attribute>
            </Attributes>
            <InnerID>13</InnerID>
            <ID>field12</ID>
            <Name>FC0000001099</Name>
            <ContentReadonly>True</ContentReadonly>
            <XElements>
                <Element xsi:type="XString" StyleIndex="5">
                    <InnerID>1222</InnerID>
                    <Text>程太生</Text>
                </Element>
            </XElements>
            <UserEditable>false</UserEditable>
            <InnerValue>程太生</InnerValue>
            <BackgroundText>患者姓名</BackgroundText>
        </Element>
        
        <!-- 主诉字段 -->
        <Element xsi:type="XInputField" StyleIndex="3">
            <Attributes>
                <Attribute>
                    <Name>FieldName</Name>
                    <Value>主诉</Value>
                </Attribute>
                <Attribute>
                    <Name>FieldCode</Name>
                    <Value>FC0000001113</Value>
                </Attribute>
                <Attribute>
                    <Name>FieldDataType</Name>
                    <Value>S1</Value>
                </Attribute>
            </Attributes>
            <InnerID>39</InnerID>
            <ID>field10637638539604323333</ID>
            <Name>FC0000001113</Name>
            <XElements>
                <Element xsi:type="XString" StyleIndex="3">
                    <InnerID>1257</InnerID>
                    <Text>认知功能下降半年，加重1周</Text>
                </Element>
            </XElements>
            <InnerValue>认知功能下降半年，加重1周</InnerValue>
            <BackgroundText>主诉在此次录入！！！</BackgroundText>
        </Element>
        
        <!-- 现病史内容字段 -->
        <Element xsi:type="XInputField">
            <Attributes>
                <Attribute>
                    <Name>FieldName</Name>
                    <Value>现病史内容</Value>
                </Attribute>
                <Attribute>
                    <Name>FieldCode</Name>
                    <Value>FC0000000850</Value>
                </Attribute>
                <Attribute>
                    <Name>FieldDataType</Name>
                    <Value>S1</Value>
                </Attribute>
            </Attributes>
            <InnerID>181</InnerID>
            <ID>field44</ID>
            <Name>FC0000000850</Name>
            <XElements>
                <Element xsi:type="XString">
                    <InnerID>1258</InnerID>
                    <Text>近半年来记忆力、理解力下降，近1周来明显加重，做饭糊锅、不知道放调料，忘记怎么使用空调电视遥控器，忘记早餐吃过什么，经门诊以"认知障碍" 收住入院。发病以来，精神状态尚可，食欲正常，睡眠正常，大便正常，小便正常，体重无变化</Text>
                </Element>
            </XElements>
            <InnerValue>近半年来记忆力、理解力下降，近1周来明显加重，做饭糊锅、不知道放调料，忘记怎么使用空调电视遥控器，忘记早餐吃过什么，经门诊以"认知障碍" 收住入院。发病以来，精神状态尚可，食欲正常，睡眠正常，大便正常，小便正常，体重无变化</InnerValue>
            <BackgroundText>现病史内容</BackgroundText>
        </Element>
        
        <!-- 记录日期时间字段 -->
        <Element xsi:type="XInputField" StyleIndex="3">
            <Attributes>
                <Attribute>
                    <Name>FieldName</Name>
                    <Value>记录日期时间</Value>
                </Attribute>
                <Attribute>
                    <Name>FieldCode</Name>
                    <Value>FC0000001100</Value>
                </Attribute>
                <Attribute>
                    <Name>FieldDataType</Name>
                    <Value>DT</Value>
                </Attribute>
                <Attribute>
                    <Name>ShowTimeFlag</Name>
                    <Value>1</Value>
                </Attribute>
            </Attributes>
            <InnerID>28</InnerID>
            <ID>field18</ID>
            <Name>FC0000001100</Name>
            <XElements>
                <Element xsi:type="XString" StyleIndex="3">
                    <InnerID>1252</InnerID>
                    <Text>2025年07月09日 22时01分</Text>
                </Element>
            </XElements>
            <DisplayFormat>
                <Style>DateTime</Style>
                <Format>yyyy年MM月dd日 HH时mm分</Format>
            </DisplayFormat>
            <InnerValue>2025/7/9 22:01:55</InnerValue>
            <BackgroundText>yyyy年MM月dd日 HH时mm分</BackgroundText>
        </Element>
    </XElements>
</XTextDocument>'''

    print("=== FC Code映射机制实际测试 ===\n")
    
    # 1. 解析XML并提取FC Code字段
    print("1. 解析XML模板，提取FC Code字段...")
    root = ET.fromstring(real_xml_template)
    fc_fields = extract_fc_codes_from_xml(root)
    
    print(f"找到 {len(fc_fields)} 个FC Code字段:\n")
    for fc_code, info in fc_fields.items():
        print(f"FC Code: {fc_code}")
        print(f"  字段名称: {info['field_name']}")
        print(f"  数据类型: {info['data_type']}")
        print(f"  当前值: {info['current_value'][:50]}{'...' if len(info['current_value']) > 50 else ''}")
        print(f"  是否可编辑: {info['is_editable']}")
        print(f"  字段分类: {info['category']}")
        print()
    
    # 2. 演示字段定位
    print("2. 演示FC Code字段定位...")
    test_fc_code = "FC0000001113"  # 主诉字段
    element = find_element_by_fc_code(root, test_fc_code)
    if element is not None:
        print(f"成功定位到FC Code {test_fc_code} 对应的XML元素")
        print(f"元素类型: {element.tag}")
        print(f"元素ID: {element.get('ID', 'N/A')}")
        
        # 显示元素的详细信息
        inner_value = element.find('.//InnerValue')
        if inner_value is not None:
            print(f"当前值: {inner_value.text}")
    else:
        print(f"未找到FC Code {test_fc_code} 对应的元素")
    
    print()
    
    # 3. 演示字段值更新
    print("3. 演示字段值更新...")
    new_value = "记忆力减退伴认知功能障碍2年，加重2周"
    success = update_field_value(element, new_value)
    if success:
        print(f"成功更新字段值为: {new_value}")
        
        # 验证更新结果
        updated_inner_value = element.find('.//InnerValue')
        updated_text = element.find('.//XElements/Element/Text')
        print(f"InnerValue: {updated_inner_value.text if updated_inner_value is not None else 'N/A'}")
        print(f"显示文本: {updated_text.text if updated_text is not None else 'N/A'}")
    else:
        print("字段值更新失败")
    
    print()
    
    # 4. 演示不同类型字段的处理策略
    print("4. 演示不同类型字段的处理策略...")
    
    # 模拟患者数据
    patient_data = {
        "name": "张三",
        "gender": "男", 
        "age": 68,
        "bed_no": "15",
        "admission_no": "574890",
        "department": "神经内科",
        "diagnosis": "阿尔茨海默病"
    }
    
    # 模拟LLM生成的内容
    llm_content = {
        "FC0000001113": "记忆力减退伴认知功能障碍2年，加重2周",
        "FC0000000850": "患者2年前开始出现记忆力减退，主要表现为近事遗忘，计算力下降。2周前症状明显加重，出现定向力障碍，日常生活能力下降。无头痛、头晕，无恶心呕吐，无肢体活动障碍。既往体健，否认高血压、糖尿病病史。"
    }
    
    # 处理不同类型的字段
    for fc_code, info in fc_fields.items():
        element = find_element_by_fc_code(root, fc_code)
        if element is None:
            continue
            
        new_value = ""
        
        if info['category'] == 'patient_info':
            # 患者信息类字段
            if fc_code == "FC0000001099":  # 患者姓名
                new_value = patient_data["name"]
            print(f"患者信息字段 {fc_code}: {info['field_name']} -> {new_value}")
            
        elif info['category'] == 'medical_content' and info['is_editable']:
            # 医疗内容类字段（可编辑）
            new_value = llm_content.get(fc_code, "")
            if new_value:
                print(f"医疗内容字段 {fc_code}: {info['field_name']} -> {new_value[:50]}...")
                
        elif info['category'] == 'datetime':
            # 日期时间类字段
            from datetime import datetime
            new_value = datetime.now().strftime("%Y年%m月%d日 %H时%M分")
            print(f"日期时间字段 {fc_code}: {info['field_name']} -> {new_value}")
        
        # 更新字段值
        if new_value and info['is_editable']:
            update_field_value(element, new_value)
    
    print()
    
    # 5. 输出最终的XML结果
    print("5. 生成最终的XML结果...")
    final_xml = ET.tostring(root, encoding='unicode')
    print("XML模板处理完成，所有FC Code字段已填充")
    
    # 显示部分结果用于验证
    print("\n=== 处理结果验证 ===")
    for fc_code in ["FC0000001099", "FC0000001113", "FC0000000850"]:
        element = find_element_by_fc_code(root, fc_code)
        if element is not None:
            inner_value = element.find('.//InnerValue')
            if inner_value is not None:
                print(f"{fc_code}: {inner_value.text[:50]}{'...' if len(inner_value.text) > 50 else ''}")


def extract_fc_codes_from_xml(root: ET.Element) -> Dict[str, Dict[str, Any]]:
    """从XML中提取所有FC Code字段信息"""
    fc_fields = {}
    
    for element in root.iter():
        fc_code = None
        field_name = None
        data_type = None
        current_value = ""
        is_editable = True
        
        # 查找FC Code
        attributes = element.find('.//Attributes')
        if attributes is not None:
            for attr in attributes.findall('Attribute'):
                name_elem = attr.find('Name')
                value_elem = attr.find('Value')
                
                if name_elem is not None and value_elem is not None:
                    if name_elem.text == 'FieldCode':
                        fc_code = value_elem.text
                    elif name_elem.text == 'FieldName':
                        field_name = value_elem.text
                    elif name_elem.text == 'FieldDataType':
                        data_type = value_elem.text
        
        if not fc_code:
            name_elem = element.find('.//Name')
            if name_elem is not None and name_elem.text and name_elem.text.startswith('FC'):
                fc_code = name_elem.text
        
        if fc_code:
            # 获取当前值
            inner_value = element.find('.//InnerValue')
            if inner_value is not None:
                current_value = inner_value.text or ""
            
            # 检查是否可编辑
            user_editable = element.find('.//UserEditable')
            content_readonly = element.find('.//ContentReadonly')
            
            if user_editable is not None and user_editable.text == 'false':
                is_editable = False
            if content_readonly is not None and content_readonly.text == 'True':
                is_editable = False
            
            # 确定字段分类
            category = categorize_fc_code(fc_code)
            
            fc_fields[fc_code] = {
                'field_name': field_name or fc_code,
                'data_type': data_type or 'S1',
                'current_value': current_value,
                'is_editable': is_editable,
                'category': category,
                'element': element
            }
    
    return fc_fields


def categorize_fc_code(fc_code: str) -> str:
    """对FC Code进行分类"""
    if fc_code in ["FC0000001099", "FC0000001108", "FC0000001105", "FC0000001092", "FC0000001114", "FC0000001104"]:
        return "patient_info"
    elif fc_code in ["FC0000001113", "FC0000000850", "FC0000000854", "FC0000000855"]:
        return "medical_content"
    elif fc_code in ["FC0000001100", "FC0000001145"]:
        return "datetime"
    elif fc_code in ["FC0000001115", "FC0000001107", "FC0000001159", "FC0000001147"]:
        return "signature"
    else:
        return "system_info"


def find_element_by_fc_code(root: ET.Element, fc_code: str) -> ET.Element:
    """根据FC Code查找对应的XML元素"""
    # 策略1: 在Attributes中查找FieldCode
    for element in root.iter():
        attributes = element.find('.//Attributes')
        if attributes is not None:
            for attr in attributes.findall('Attribute'):
                name_elem = attr.find('Name')
                value_elem = attr.find('Value')
                
                if (name_elem is not None and value_elem is not None and 
                    name_elem.text == 'FieldCode' and value_elem.text == fc_code):
                    return element
    
    # 策略2: 在Name中查找
    for element in root.iter():
        name_elem = element.find('.//Name')
        if name_elem is not None and name_elem.text == fc_code:
            return element
    
    return None


def update_field_value(element: ET.Element, new_value: str) -> bool:
    """更新XML元素的值"""
    try:
        # 更新InnerValue
        inner_value = element.find('.//InnerValue')
        if inner_value is not None:
            inner_value.text = new_value
        
        # 更新XString/Text
        xstring_text = element.find('.//XElements/Element/Text')
        if xstring_text is not None:
            xstring_text.text = new_value
        
        return True
    except Exception as e:
        print(f"更新字段值失败: {e}")
        return False


if __name__ == "__main__":
    test_fc_code_mapping_with_real_data()
