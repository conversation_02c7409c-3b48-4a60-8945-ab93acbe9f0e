medical_templates/
├── __init__.py
├── base/
│   ├── __init__.py
│   ├── template_base.py          # 基础模板类
│   ├── fc_code_field.py          # FC Code字段定义
│   └── template_types.py         # 模板类型枚举
├── templates/
│   ├── __init__.py
│   ├── admission_record.py       # 入院记录模板
│   ├── progress_note.py          # 病程记录模板
│   ├── ward_round.py            # 查房记录模板
│   ├── discharge_record.py       # 出院记录模板
│   └── consultation.py          # 会诊记录模板
├── processors/
│   ├── __init__.py
│   ├── xml_processor.py          # XML处理器
│   ├── fc_code_parser.py         # FC Code解析器
│   └── llm_generator.py          # LLM内容生成器
├── factory/
│   ├── __init__.py
│   └── template_factory.py       # 模板工厂
├── validators/
│   ├── __init__.py
│   ├── content_validator.py      # 内容验证器
│   └── field_validator.py        # 字段验证器
└── utils/
    ├── __init__.py
    ├── xml_utils.py              # XML工具函数
    └── date_utils.py             # 日期工具函数