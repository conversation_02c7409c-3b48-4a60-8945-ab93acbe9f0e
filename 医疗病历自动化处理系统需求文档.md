# 医疗病历自动化处理系统需求文档 v2.0

## 1. 项目概述

### 1.1 项目背景
基于对现有医疗信息系统的抓包分析，开发一个基于Python的医疗病历自动化处理系统，采用MCP（Model Context Protocol）微服务架构，实现与医院HIS系统的无缝对接，通过LLM技术自动填写病历内容，提高医生工作效率。

### 1.2 项目目标
- 实现与医院服务器的安全认证和通信
- 自动获取和管理病历模板
- 智能填写病历内容（当前阶段专注于病程记录）
- 提供完整的病历生命周期管理
- 建立本地数据存储和日志监控体系

### 1.3 当前阶段实施范围
**Phase 1: 病程记录自动化处理**
- **首次病程记录**：患者入院后的首次病程记录自动生成
- **主治医师查房记录**：定期查房记录的自动化处理
- **暂不包含**：入院记录、出院记录、手术记录等其他类型病历

### 1.4 技术栈
- **编程语言**: Python 3.8+
- **微服务架构**: MCP (Model Context Protocol)
- **HTTP客户端**: httpx
- **协议**: HTTP/1.1
- **数据格式**: JSON, XML
- **AI集成**: LLM API (OpenAI/本地模型)
- **数据处理**: lxml, json
- **数据库**: PostgreSQL 14+
- **ORM框架**: SQLAlchemy 2.0+

## 2. MCP微服务架构设计

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        AI Agent 工作流程层                      │
├─────────────────────────────────────────────────────────────────┤
│ 输入处理 → 患者查询 → 病历列表 → LLM生成 → 内容验证 → 保存提交  │
└─────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────┐
│                        MCP 服务层                               │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│ Auth MCP        │ Patient MCP     │ Medical Record  │ Template  │
│ Service         │ Service         │ MCP Service     │ MCP       │
│                 │                 │                 │ Service   │
│ • OAuth2认证    │ • 患者信息查询  │ • 病历列表管理  │ • 模板解析│
│ • Token管理     │ • visitID获取   │ • 病程记录CRUD  │ • FC Code │
│ • 会话保持      │ • 检查信息      │ • 权限验证      │ • LLM生成 │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────┐
│                        数据存储层                               │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│ PostgreSQL      │ 医院HIS系统     │ OSS文件存储     │ 缓存系统  │
│ 本地数据库      │ API接口         │ XML文件         │ Redis     │
│                 │                 │                 │           │
│ • 患者住院号    │ • 患者信息      │ • 病历XML      │ • 模板缓存│
│ • 医生输入      │ • 病历列表      │ • 模板文件      │ • 会话缓存│
│ • 操作日志      │ • 认证服务      │                 │           │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
```

### 2.2 MCP服务划分策略

#### 2.2.1 认证与会话管理服务 (Auth MCP Service)
**职责边界**：
- OAuth2.0双重认证流程
- Bearer Token生命周期管理
- 会话状态维护和刷新
- 跨服务认证状态共享

**独立性考虑**：
- 无业务逻辑依赖，纯认证服务
- 可被所有其他MCP服务复用
- 支持独立部署和扩展

#### 2.2.2 患者信息服务 (Patient MCP Service)
**职责边界**：
- 根据住院号查询患者visittime
- 生成patNO_visittime标识符
- 通过标识符获取visitID
- 患者基本信息和检查信息查询

**数据流程**：
```
住院号 → visittime → patNO_visittime → visitID → 完整患者信息
```

#### 2.2.3 病历管理服务 (Medical Record MCP Service)
**职责边界**：
- 通过visitID获取患者完整病历列表
- 病程记录的CRUD操作
- 病历权限验证和状态管理
- XML+JSON双重保存流程

**专注领域**：当前阶段仅处理病程记录类型

#### 2.2.4 病历模板处理服务 (Template Processing MCP Service)
**职责边界**：
- 病程记录模板的解析和管理
- FC Code字段映射和处理
- LLM内容生成和验证
- 时效性约束和质量控制

## 3. AI Agent工作流程设计

### 3.1 核心工作流程

#### 3.1.1 输入处理阶段
**输入**: 用户提供的基础住院号
**处理逻辑**:
```python
def process_input(admission_no: str) -> Dict[str, Any]:
    """
    处理用户输入的住院号

    Args:
        admission_no: 基础住院号 (如: 574677)

    Returns:
        处理结果包含验证状态和标准化住院号
    """
    # 1. 输入验证和标准化
    # 2. 格式检查和清理
    # 3. 返回标准化结果
```

#### 3.1.2 患者信息查询阶段
**核心流程**:
```
住院号 → 查询visittime → 生成patNO_visittime → 获取visitID → 病历列表
```

**详细步骤**:
1. **查询visittime**: 根据住院号查询患者的就诊时间标识
2. **生成标识符**: 组合生成 `{patNO}_{visittime}` 格式标识符
3. **获取visitID**: 使用标识符获取系统内部visitID
4. **获取病历列表**: 通过visitID获取患者完整病历列表

#### 3.1.3 LLM内容生成阶段
**生成策略**:
- **首次病程记录**: 基于患者入院信息和诊断生成
- **查房记录**: 基于病情变化和治疗进展生成
- **时效性约束**: 确保内容符合医疗时间要求

**提示词设计原则**:
```python
PROMPT_TEMPLATES = {
    "first_progress_note": """
    基于以下患者信息生成首次病程记录：
    - 患者：{name}，{age}岁，{gender}
    - 住院号：{admission_no}
    - 诊断：{diagnosis}
    - 入院时间：{admission_time}

    要求：
    1. 记录患者当前病情状态
    2. 描述入院后的初步处理
    3. 制定后续治疗计划
    4. 字数控制在300-500字
    5. 符合医疗记录规范
    """,

    "ward_round_record": """
    生成主治医师查房记录：
    - 患者：{name}，{age}岁，{gender}
    - 入院天数：{days_since_admission}天
    - 当前诊断：{current_diagnosis}
    - 治疗情况：{treatment_status}
    - 病情变化：{condition_changes}

    时效性要求：
    1. 病情稳定患者：至少3天1次查房记录
    2. 病情变化患者：每日查房记录
    3. 危重患者：每日多次记录

    内容要求：
    1. 评估当前病情
    2. 分析治疗效果
    3. 调整治疗方案
    4. 预后评估
    """
}
```

### 3.2 决策逻辑设计

#### 3.2.1 病程记录类型判断
```python
def determine_progress_note_type(patient_info: dict, medical_history: list) -> str:
    """
    判断需要生成的病程记录类型

    Args:
        patient_info: 患者基本信息
        medical_history: 既往病历记录

    Returns:
        病程记录类型 ('first_progress', 'ward_round', 'daily_progress')
    """
    # 1. 检查是否为首次病程记录
    # 2. 分析上次记录时间间隔
    # 3. 评估病情严重程度
    # 4. 返回推荐的记录类型
```

#### 3.2.2 时效性约束检查
```python
def check_timing_constraints(last_record_time: datetime, patient_condition: str) -> dict:
    """
    检查病程记录的时效性要求

    Args:
        last_record_time: 上次记录时间
        patient_condition: 患者病情状态 ('stable', 'changing', 'critical')

    Returns:
        时效性检查结果和建议
    """
    current_time = datetime.now()
    time_diff = current_time - last_record_time

    constraints = {
        'stable': timedelta(days=3),      # 稳定患者3天一次
        'changing': timedelta(days=1),    # 病情变化每日一次
        'critical': timedelta(hours=8)    # 危重患者8小时一次
    }

    required_interval = constraints.get(patient_condition, timedelta(days=1))
    is_overdue = time_diff > required_interval

    return {
        'is_overdue': is_overdue,
        'time_since_last': time_diff,
        'required_interval': required_interval,
        'urgency_level': 'high' if is_overdue else 'normal'
    }
```

### 3.3 质量控制机制

#### 3.3.1 内容验证规则
```python
def validate_progress_note_content(content: str, note_type: str) -> List[str]:
    """
    验证病程记录内容质量

    Args:
        content: 生成的病程记录内容
        note_type: 记录类型

    Returns:
        验证错误列表
    """
    errors = []

    # 1. 长度检查
    if len(content) < 100:
        errors.append("病程记录内容过短，应不少于100字")

    # 2. 必要元素检查
    required_elements = {
        'first_progress': ['病情评估', '治疗计划', '观察要点'],
        'ward_round': ['病情变化', '治疗效果', '下一步计划']
    }

    for element in required_elements.get(note_type, []):
        if element not in content:
            errors.append(f"缺少必要内容：{element}")

    # 3. 医疗术语检查
    # 4. 逻辑一致性检查

    return errors
```

## 4. 病历模板类库设计

### 4.1 模板类继承体系

#### 4.1.1 基础抽象类设计
```python
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class ProgressNoteType(Enum):
    """病程记录类型枚举（当前阶段专用）"""
    FIRST_PROGRESS = "first_progress"          # 首次病程记录
    WARD_ROUND = "ward_round"                  # 主治医师查房记录
    DAILY_PROGRESS = "daily_progress"          # 日常病程记录
    CONDITION_CHANGE = "condition_change"      # 病情变化记录

@dataclass
class FCCodeField:
    """FC Code字段定义"""
    fc_code: str
    field_name: str
    field_type: str
    is_required: bool
    is_editable: bool
    default_value: Optional[str] = None
    llm_template: Optional[str] = None
    validation_rules: Optional[Dict[str, Any]] = None

class BaseProgressNoteTemplate(ABC):
    """病程记录模板基类"""

    def __init__(self, template_id: str, template_name: str):
        self.template_id = template_id
        self.template_name = template_name
        self.fc_code_fields: Dict[str, FCCodeField] = {}
        self.xml_content: Optional[str] = None

    @abstractmethod
    def get_note_type(self) -> ProgressNoteType:
        """获取病程记录类型"""
        pass

    @abstractmethod
    def get_required_fields(self) -> List[str]:
        """获取必填字段列表"""
        pass

    @abstractmethod
    def get_llm_fields(self) -> List[str]:
        """获取需要LLM生成的字段列表"""
        pass

    @abstractmethod
    def get_timing_constraints(self) -> Dict[str, Any]:
        """获取时效性约束"""
        pass

    @abstractmethod
    def validate_content(self, content: Dict[str, str]) -> List[str]:
        """验证内容，返回错误信息列表"""
        pass
```

#### 4.1.2 具体模板类实现
```python
class FirstProgressNoteTemplate(BaseProgressNoteTemplate):
    """首次病程记录模板"""

    def get_note_type(self) -> ProgressNoteType:
        return ProgressNoteType.FIRST_PROGRESS

    def get_required_fields(self) -> List[str]:
        return [
            "FC0000001100",  # 记录日期时间
            "FC0000001144",  # 病程记录内容
            "FC0000001147",  # 记录人签名
            "FC0000001159",  # 主治医师签名
        ]

    def get_llm_fields(self) -> List[str]:
        return [
            "FC0000001144",  # 病程记录内容
        ]

    def get_timing_constraints(self) -> Dict[str, Any]:
        return {
            "max_delay_hours": 24,  # 入院后24小时内必须完成
            "min_content_length": 200,
            "required_elements": [
                "入院诊断", "病情评估", "治疗计划", "观察要点"
            ]
        }

    def validate_content(self, content: Dict[str, str]) -> List[str]:
        errors = []

        # 检查必填字段
        for field_code in self.get_required_fields():
            if field_code not in content or not content[field_code].strip():
                field_name = self.fc_code_fields.get(field_code, {}).field_name or field_code
                errors.append(f"必填字段 {field_name} 不能为空")

        # 检查首次病程记录特定要求
        if "FC0000001144" in content:
            note_content = content["FC0000001144"]
            constraints = self.get_timing_constraints()

            if len(note_content) < constraints["min_content_length"]:
                errors.append(f"首次病程记录内容不能少于{constraints['min_content_length']}字")

            for element in constraints["required_elements"]:
                if element not in note_content:
                    errors.append(f"首次病程记录应包含：{element}")

        return errors

class WardRoundTemplate(BaseProgressNoteTemplate):
    """主治医师查房记录模板"""

    def get_note_type(self) -> ProgressNoteType:
        return ProgressNoteType.WARD_ROUND

    def get_required_fields(self) -> List[str]:
        return [
            "FC0000001145",  # 查房日期时间
            "FC0000001144",  # 查房记录内容
            "FC0000001159",  # 主治医师签名
        ]

    def get_llm_fields(self) -> List[str]:
        return [
            "FC0000001144",  # 查房记录内容
        ]

    def get_timing_constraints(self) -> Dict[str, Any]:
        return {
            "stable_patient_interval_days": 3,    # 稳定患者3天一次
            "changing_patient_interval_days": 1,  # 病情变化患者每日一次
            "critical_patient_interval_hours": 8, # 危重患者8小时一次
            "min_content_length": 150,
            "required_elements": [
                "病情评估", "治疗效果", "下一步计划"
            ]
        }

    def validate_content(self, content: Dict[str, str]) -> List[str]:
        errors = []

        # 查房记录特定验证逻辑
        if "FC0000001144" in content:
            content_text = content["FC0000001144"]
            constraints = self.get_timing_constraints()

            if len(content_text) < constraints["min_content_length"]:
                errors.append(f"查房记录内容不能少于{constraints['min_content_length']}字")

            for element in constraints["required_elements"]:
                if element not in content_text:
                    errors.append(f"查房记录应包含：{element}")

        return errors
```

### 4.2 模板工厂和管理器
```python
class ProgressNoteTemplateFactory:
    """病程记录模板工厂类"""

    _template_classes = {
        ProgressNoteType.FIRST_PROGRESS: FirstProgressNoteTemplate,
        ProgressNoteType.WARD_ROUND: WardRoundTemplate,
        # 后续可扩展其他病程记录类型
    }

    @classmethod
    def create_template(cls, note_type: ProgressNoteType,
                       template_id: str, template_name: str) -> BaseProgressNoteTemplate:
        """创建病程记录模板实例"""
        template_class = cls._template_classes.get(note_type)
        if not template_class:
            raise ValueError(f"不支持的病程记录类型: {note_type}")

        return template_class(template_id, template_name)

    @classmethod
    def get_supported_types(cls) -> List[ProgressNoteType]:
        """获取支持的病程记录类型列表"""
        return list(cls._template_classes.keys())
```

## 5. PostgreSQL数据库设计

### 5.1 数据库架构概述

由于医院服务器缺少患者列表查询API，系统需要建立本地PostgreSQL数据库来存储和管理相关数据。

### 5.2 核心数据表设计

#### 5.2.1 患者信息表 (patients)
```sql
CREATE TABLE patients (
    id BIGSERIAL PRIMARY KEY,
    admission_no VARCHAR(50) NOT NULL UNIQUE,           -- 住院号
    patient_name VARCHAR(100) NOT NULL,                 -- 患者姓名
    gender VARCHAR(10),                                  -- 性别
    age INTEGER,                                         -- 年龄
    department VARCHAR(100),                             -- 科室
    bed_no VARCHAR(20),                                  -- 床号
    diagnosis TEXT,                                      -- 诊断
    visit_time INTEGER,                                  -- 就诊时间标识
    visit_id VARCHAR(100),                               -- 系统visitID
    pat_no_visit_time VARCHAR(150),                      -- patNO_visittime标识符
    admission_date TIMESTAMP,                            -- 入院日期
    discharge_date TIMESTAMP,                            -- 出院日期
    patient_status VARCHAR(20) DEFAULT 'active',        -- 患者状态
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_admission_no (admission_no),
    INDEX idx_visit_id (visit_id),
    INDEX idx_pat_no_visit_time (pat_no_visit_time),
    INDEX idx_patient_status (patient_status)
);
```

#### 5.2.2 医生输入信息表 (doctor_inputs)
```sql
CREATE TABLE doctor_inputs (
    id BIGSERIAL PRIMARY KEY,
    patient_id BIGINT NOT NULL REFERENCES patients(id),
    doctor_id VARCHAR(50) NOT NULL,                     -- 医生工号
    doctor_name VARCHAR(100) NOT NULL,                  -- 医生姓名
    input_type VARCHAR(50) NOT NULL,                    -- 输入类型
    input_content TEXT NOT NULL,                        -- 输入内容
    input_context JSONB,                                -- 输入上下文(JSON格式)
    priority_level INTEGER DEFAULT 1,                   -- 优先级(1-5)
    is_processed BOOLEAN DEFAULT FALSE,                 -- 是否已处理
    processed_at TIMESTAMP,                             -- 处理时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_patient_doctor (patient_id, doctor_id),
    INDEX idx_input_type (input_type),
    INDEX idx_is_processed (is_processed),
    INDEX idx_created_at (created_at)
);
```

#### 5.2.3 病程记录表 (progress_notes)
```sql
CREATE TABLE progress_notes (
    id BIGSERIAL PRIMARY KEY,
    patient_id BIGINT NOT NULL REFERENCES patients(id),
    note_type VARCHAR(50) NOT NULL,                     -- 记录类型
    template_id VARCHAR(100),                           -- 模板ID
    template_name VARCHAR(200),                         -- 模板名称
    record_date TIMESTAMP NOT NULL,                     -- 记录日期
    content TEXT NOT NULL,                              -- 记录内容
    fc_code_data JSONB,                                 -- FC Code数据
    xml_content TEXT,                                   -- XML内容
    file_id VARCHAR(200),                               -- OSS文件ID
    index_id VARCHAR(100),                              -- 病历索引ID
    author_id VARCHAR(50) NOT NULL,                     -- 记录人ID
    author_name VARCHAR(100) NOT NULL,                  -- 记录人姓名
    status VARCHAR(20) DEFAULT 'draft',                 -- 状态(draft/saved/submitted)
    is_generated_by_ai BOOLEAN DEFAULT FALSE,           -- 是否AI生成
    generation_model VARCHAR(100),                      -- 生成模型
    quality_score DECIMAL(3,2),                         -- 质量评分
    validation_errors JSONB,                            -- 验证错误
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_patient_note_type (patient_id, note_type),
    INDEX idx_record_date (record_date),
    INDEX idx_status (status),
    INDEX idx_is_generated_by_ai (is_generated_by_ai),
    INDEX idx_author_id (author_id)
);
```

#### 5.2.4 系统操作日志表 (operation_logs)
```sql
CREATE TABLE operation_logs (
    id BIGSERIAL PRIMARY KEY,
    operation_type VARCHAR(50) NOT NULL,               -- 操作类型
    operation_name VARCHAR(200) NOT NULL,              -- 操作名称
    user_id VARCHAR(50),                                -- 操作用户ID
    user_name VARCHAR(100),                             -- 操作用户名
    patient_id BIGINT REFERENCES patients(id),         -- 关联患者ID
    target_type VARCHAR(50),                            -- 目标类型
    target_id VARCHAR(100),                             -- 目标ID
    operation_data JSONB,                               -- 操作数据
    request_data JSONB,                                 -- 请求数据
    response_data JSONB,                                -- 响应数据
    execution_time_ms INTEGER,                          -- 执行时间(毫秒)
    status VARCHAR(20) NOT NULL,                        -- 状态(success/failed/pending)
    error_message TEXT,                                 -- 错误信息
    ip_address INET,                                    -- IP地址
    user_agent TEXT,                                    -- 用户代理
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 索引
    INDEX idx_operation_type (operation_type),
    INDEX idx_user_id (user_id),
    INDEX idx_patient_id (patient_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

#### 5.2.5 AI Agent运行记录表 (agent_runs)
```sql
CREATE TABLE agent_runs (
    id BIGSERIAL PRIMARY KEY,
    run_id VARCHAR(100) NOT NULL UNIQUE,               -- 运行ID
    patient_id BIGINT NOT NULL REFERENCES patients(id),
    trigger_type VARCHAR(50) NOT NULL,                 -- 触发类型
    trigger_data JSONB,                                 -- 触发数据
    workflow_steps JSONB NOT NULL,                     -- 工作流步骤
    current_step VARCHAR(100),                          -- 当前步骤
    total_steps INTEGER,                                -- 总步骤数
    completed_steps INTEGER DEFAULT 0,                 -- 已完成步骤
    status VARCHAR(20) DEFAULT 'running',              -- 状态
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    execution_time_seconds INTEGER,                     -- 执行时间(秒)
    success_rate DECIMAL(5,2),                         -- 成功率
    error_count INTEGER DEFAULT 0,                     -- 错误次数
    warning_count INTEGER DEFAULT 0,                   -- 警告次数
    generated_content_count INTEGER DEFAULT 0,         -- 生成内容数量
    final_result JSONB,                                 -- 最终结果

    -- 索引
    INDEX idx_run_id (run_id),
    INDEX idx_patient_id (patient_id),
    INDEX idx_trigger_type (trigger_type),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time)
);
```

### 5.3 数据模型关系图
```
patients (1) ←→ (N) doctor_inputs
patients (1) ←→ (N) progress_notes
patients (1) ←→ (N) operation_logs
patients (1) ←→ (N) agent_runs

progress_notes (1) ←→ (N) operation_logs (通过target_id关联)
agent_runs (1) ←→ (N) operation_logs (通过target_id关联)
```

### 5.4 数据库配置和优化

#### 5.4.1 连接池配置
```python
# database.py
from sqlalchemy import create_engine
from sqlalchemy.pool import QueuePool

DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'database': 'medical_records',
    'username': 'medical_user',
    'password': 'secure_password',
    'pool_size': 10,
    'max_overflow': 20,
    'pool_timeout': 30,
    'pool_recycle': 3600
}

engine = create_engine(
    f"postgresql://{DATABASE_CONFIG['username']}:{DATABASE_CONFIG['password']}@"
    f"{DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}",
    poolclass=QueuePool,
    pool_size=DATABASE_CONFIG['pool_size'],
    max_overflow=DATABASE_CONFIG['max_overflow'],
    pool_timeout=DATABASE_CONFIG['pool_timeout'],
    pool_recycle=DATABASE_CONFIG['pool_recycle']
)
```

#### 5.4.2 数据备份策略
```bash
# 每日备份脚本
#!/bin/bash
BACKUP_DIR="/var/backups/medical_records"
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U medical_user medical_records > "$BACKUP_DIR/backup_$DATE.sql"

# 保留最近30天的备份
find $BACKUP_DIR -name "backup_*.sql" -mtime +30 -delete
```

## 6. MCP服务接口定义

### 6.1 认证与会话管理服务 (Auth MCP Service)

#### 6.1.1 服务接口
```python
# MCP工具定义
@mcp_tool
async def authenticate_client(client_id: str, client_secret: str) -> dict:
    """
    客户端认证

    Args:
        client_id: 客户端ID
        client_secret: 客户端密钥

    Returns:
        认证结果包含access_token
    """

@mcp_tool
async def authenticate_user(user_id: str, client_token: str) -> dict:
    """
    用户认证

    Args:
        user_id: 用户ID
        client_token: 客户端令牌

    Returns:
        用户认证结果包含用户token和权限信息
    """

@mcp_tool
async def refresh_token(refresh_token: str) -> dict:
    """刷新访问令牌"""

@mcp_tool
async def validate_token(access_token: str) -> bool:
    """验证Token有效性"""
```

### 6.2 患者信息服务 (Patient MCP Service)

#### 6.2.1 核心工作流程接口
```python
@mcp_tool
async def query_patient_by_admission_no(admission_no: str) -> dict:
    """
    根据住院号查询患者信息

    Args:
        admission_no: 住院号

    Returns:
        患者基本信息和visittime
    """

@mcp_tool
async def generate_patient_identifier(pat_no: str, visit_time: int) -> str:
    """
    生成patNO_visittime标识符

    Args:
        pat_no: 患者编号
        visit_time: 就诊时间标识

    Returns:
        格式化的患者标识符
    """

@mcp_tool
async def get_visit_id(patient_identifier: str) -> str:
    """
    通过患者标识符获取visitID

    Args:
        patient_identifier: patNO_visittime格式标识符

    Returns:
        系统内部visitID
    """

@mcp_tool
async def get_patient_medical_records(visit_id: str) -> List[dict]:
    """
    通过visitID获取患者完整病历列表

    Args:
        visit_id: 系统visitID

    Returns:
        患者病历列表
    """
```

### 6.3 病历管理服务 (Medical Record MCP Service)

#### 6.3.1 病程记录专用接口
```python
@mcp_tool
async def get_progress_note_list(patient_id: str, note_type: str = None) -> List[dict]:
    """
    获取病程记录列表

    Args:
        patient_id: 患者ID
        note_type: 记录类型筛选

    Returns:
        病程记录列表
    """

@mcp_tool
async def create_progress_note(patient_id: str, note_type: str,
                              template_id: str, initial_data: dict) -> dict:
    """
    创建新的病程记录

    Args:
        patient_id: 患者ID
        note_type: 记录类型
        template_id: 模板ID
        initial_data: 初始数据

    Returns:
        创建结果
    """

@mcp_tool
async def update_progress_note(record_id: str, updates: dict) -> dict:
    """更新病程记录内容"""

@mcp_tool
async def save_progress_note(record_id: str, xml_content: str,
                            fc_code_data: dict) -> dict:
    """
    保存病程记录（XML + JSON双重保存）

    Args:
        record_id: 记录ID
        xml_content: XML内容
        fc_code_data: FC Code数据

    Returns:
        保存结果
    """

@mcp_tool
async def validate_progress_note_timing(patient_id: str, note_type: str) -> dict:
    """
    验证病程记录时效性

    Args:
        patient_id: 患者ID
        note_type: 记录类型

    Returns:
        时效性检查结果
    """
```

### 6.4 病历模板处理服务 (Template Processing MCP Service)

#### 6.4.1 模板处理接口
```python
@mcp_tool
async def get_progress_note_templates(dept_code: str) -> List[dict]:
    """
    获取病程记录模板列表

    Args:
        dept_code: 科室代码

    Returns:
        病程记录模板列表
    """

@mcp_tool
async def load_progress_note_template(template_id: str, note_type: str) -> dict:
    """
    加载病程记录模板

    Args:
        template_id: 模板ID
        note_type: 记录类型

    Returns:
        模板实例和FC Code字段信息
    """

@mcp_tool
async def generate_progress_note_content(note_type: str, patient_data: dict,
                                        context: dict) -> dict:
    """
    生成病程记录内容

    Args:
        note_type: 记录类型
        patient_data: 患者数据
        context: 生成上下文

    Returns:
        生成的内容和质量评估
    """

@mcp_tool
async def validate_progress_note_content(note_type: str, content: dict) -> List[str]:
    """
    验证病程记录内容

    Args:
        note_type: 记录类型
        content: 记录内容

    Returns:
        验证错误列表
    """

@mcp_tool
async def process_complete_progress_note(template_id: str, note_type: str,
                                        patient_data: dict, context: dict) -> dict:
    """
    完整处理病程记录（一站式服务）

    Args:
        template_id: 模板ID
        note_type: 记录类型
        patient_data: 患者数据
        context: 处理上下文

    Returns:
        完整的处理结果
    """
```

## 7. LLM提示词设计策略

### 7.1 提示词模板体系

#### 7.1.1 首次病程记录提示词
```python
FIRST_PROGRESS_NOTE_PROMPT = """
你是一名经验丰富的临床医生，需要为患者编写首次病程记录。

患者信息：
- 姓名：{patient_name}
- 性别：{gender}，年龄：{age}岁
- 住院号：{admission_no}
- 科室：{department}
- 床号：{bed_no}
- 入院诊断：{diagnosis}
- 入院时间：{admission_time}

医生输入的关键信息：
{doctor_inputs}

时效性要求：
- 必须在患者入院后24小时内完成
- 记录时间：{current_time}
- 距离入院时间：{hours_since_admission}小时

内容要求：
1. 病情评估：详细描述患者当前病情状态
2. 入院诊断：确认或调整入院诊断
3. 治疗计划：制定详细的治疗方案
4. 观察要点：明确需要重点观察的指标
5. 预后评估：初步评估患者预后

格式要求：
- 总字数：300-500字
- 语言：专业医学术语，简洁明了
- 结构：按照病情评估→诊断→治疗→观察→预后的顺序

请生成符合医疗规范的首次病程记录：
"""

WARD_ROUND_PROMPT = """
你是一名主治医师，需要编写查房记录。

患者基本信息：
- 姓名：{patient_name}，{gender}，{age}岁
- 住院号：{admission_no}，床号：{bed_no}
- 入院诊断：{admission_diagnosis}
- 入院日期：{admission_date}
- 住院天数：{days_hospitalized}天

病情变化情况：
{condition_changes}

当前治疗情况：
{current_treatment}

检查结果：
{examination_results}

时效性约束：
- 患者病情状态：{patient_condition}  # stable/changing/critical
- 上次查房时间：{last_round_time}
- 距离上次查房：{hours_since_last_round}小时
- 建议查房频率：{recommended_frequency}

内容要求：
1. 病情评估：分析当前病情变化
2. 治疗效果：评估现有治疗的效果
3. 问题识别：发现需要关注的问题
4. 调整方案：提出治疗方案调整建议
5. 下一步计划：明确后续治疗计划

质量标准：
- 字数：200-400字
- 必须包含：病情评估、治疗效果、下一步计划
- 避免：模糊表述、缺乏具体指导

请生成专业的主治医师查房记录：
"""
```

### 7.2 时效性约束机制

#### 7.2.1 时间约束规则
```python
class TimingConstraints:
    """时效性约束规则"""

    FIRST_PROGRESS_NOTE = {
        'max_delay_hours': 24,
        'warning_threshold_hours': 18,
        'critical_threshold_hours': 22
    }

    WARD_ROUND_INTERVALS = {
        'stable': {'days': 3, 'max_delay_hours': 6},
        'changing': {'days': 1, 'max_delay_hours': 4},
        'critical': {'hours': 8, 'max_delay_hours': 2}
    }

    @staticmethod
    def check_first_progress_timing(admission_time: datetime) -> dict:
        """检查首次病程记录时效性"""
        current_time = datetime.now()
        hours_elapsed = (current_time - admission_time).total_seconds() / 3600

        constraints = TimingConstraints.FIRST_PROGRESS_NOTE

        if hours_elapsed > constraints['critical_threshold_hours']:
            urgency = 'critical'
            message = f"严重超时！已超过{hours_elapsed:.1f}小时，必须立即完成"
        elif hours_elapsed > constraints['warning_threshold_hours']:
            urgency = 'warning'
            message = f"接近超时！已过{hours_elapsed:.1f}小时，请尽快完成"
        else:
            urgency = 'normal'
            message = f"时间充足，已过{hours_elapsed:.1f}小时"

        return {
            'urgency_level': urgency,
            'hours_elapsed': hours_elapsed,
            'message': message,
            'is_overdue': hours_elapsed > constraints['max_delay_hours']
        }
```

### 7.3 内容质量控制

#### 7.3.1 质量评估指标
```python
class ContentQualityAssessment:
    """内容质量评估"""

    @staticmethod
    def assess_progress_note_quality(content: str, note_type: str) -> dict:
        """评估病程记录质量"""

        # 基础指标
        word_count = len(content)
        sentence_count = len([s for s in content.split('。') if s.strip()])

        # 必要元素检查
        required_elements = {
            'first_progress': ['病情', '诊断', '治疗', '观察', '预后'],
            'ward_round': ['病情', '治疗效果', '计划']
        }

        elements = required_elements.get(note_type, [])
        missing_elements = [elem for elem in elements if elem not in content]

        # 医学术语密度
        medical_terms = ['症状', '体征', '诊断', '治疗', '药物', '检查', '监测']
        term_count = sum(1 for term in medical_terms if term in content)
        term_density = term_count / len(medical_terms)

        # 计算综合评分
        scores = {
            'length_score': min(100, (word_count / 300) * 100),
            'completeness_score': ((len(elements) - len(missing_elements)) / len(elements)) * 100 if elements else 100,
            'terminology_score': term_density * 100,
            'structure_score': min(100, sentence_count * 20)  # 假设5句话为满分
        }

        overall_score = sum(scores.values()) / len(scores)

        return {
            'overall_score': round(overall_score, 2),
            'detailed_scores': scores,
            'word_count': word_count,
            'missing_elements': missing_elements,
            'quality_level': 'excellent' if overall_score >= 90 else
                           'good' if overall_score >= 75 else
                           'acceptable' if overall_score >= 60 else 'poor'
        }
```

### 7.4 上下文管理策略

#### 7.4.1 动态上下文构建
```python
class ContextManager:
    """上下文管理器"""

    @staticmethod
    def build_progress_note_context(patient_id: str, note_type: str) -> dict:
        """构建病程记录生成上下文"""

        # 获取患者基本信息
        patient_info = get_patient_info(patient_id)

        # 获取历史病程记录
        history_notes = get_patient_progress_notes(patient_id, limit=5)

        # 获取最新检查结果
        recent_exams = get_recent_examinations(patient_id, days=7)

        # 获取医生输入
        doctor_inputs = get_doctor_inputs(patient_id, processed=False)

        # 分析病情趋势
        condition_trend = analyze_condition_trend(history_notes)

        context = {
            'patient_info': patient_info,
            'history_summary': summarize_history_notes(history_notes),
            'recent_changes': extract_recent_changes(history_notes),
            'examination_results': format_exam_results(recent_exams),
            'doctor_inputs': format_doctor_inputs(doctor_inputs),
            'condition_trend': condition_trend,
            'timing_info': calculate_timing_info(patient_info, note_type)
        }

        return context
```

## 8. 系统日志记录和监控方案

### 8.1 日志记录体系

#### 8.1.1 日志分类和级别
```python
import logging
from enum import Enum

class LogLevel(Enum):
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class LogCategory(Enum):
    AUTHENTICATION = "auth"
    PATIENT_QUERY = "patient"
    TEMPLATE_PROCESSING = "template"
    LLM_GENERATION = "llm"
    MEDICAL_RECORD = "record"
    DATABASE = "database"
    SYSTEM = "system"
    PERFORMANCE = "performance"

# 日志配置
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(category)s - %(message)s - %(extra_data)s'
        },
        'simple': {
            'format': '%(asctime)s - %(levelname)s - %(message)s'
        }
    },
    'handlers': {
        'file_handler': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/medical_system.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 10,
            'formatter': 'detailed'
        },
        'error_handler': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/errors.log',
            'maxBytes': 5242880,   # 5MB
            'backupCount': 5,
            'formatter': 'detailed',
            'level': 'ERROR'
        },
        'performance_handler': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/performance.log',
            'maxBytes': 10485760,
            'backupCount': 5,
            'formatter': 'detailed'
        }
    },
    'loggers': {
        'medical_system': {
            'handlers': ['file_handler', 'error_handler'],
            'level': 'INFO',
            'propagate': False
        },
        'performance': {
            'handlers': ['performance_handler'],
            'level': 'INFO',
            'propagate': False
        }
    }
}
```

#### 8.1.2 结构化日志记录
```python
class StructuredLogger:
    """结构化日志记录器"""

    def __init__(self, category: LogCategory):
        self.category = category
        self.logger = logging.getLogger('medical_system')

    def log_operation(self, level: LogLevel, operation: str,
                     user_id: str = None, patient_id: str = None,
                     execution_time: float = None, **kwargs):
        """记录操作日志"""

        extra_data = {
            'operation': operation,
            'user_id': user_id,
            'patient_id': patient_id,
            'execution_time_ms': execution_time * 1000 if execution_time else None,
            'category': self.category.value,
            **kwargs
        }

        message = f"Operation: {operation}"
        if user_id:
            message += f" | User: {user_id}"
        if patient_id:
            message += f" | Patient: {patient_id}"
        if execution_time:
            message += f" | Time: {execution_time:.3f}s"

        self.logger.log(
            getattr(logging, level.value),
            message,
            extra={'extra_data': extra_data}
        )

    def log_llm_generation(self, prompt_type: str, patient_id: str,
                          generation_time: float, token_count: int,
                          quality_score: float, success: bool):
        """记录LLM生成日志"""

        self.log_operation(
            LogLevel.INFO,
            "LLM_CONTENT_GENERATION",
            patient_id=patient_id,
            execution_time=generation_time,
            prompt_type=prompt_type,
            token_count=token_count,
            quality_score=quality_score,
            success=success
        )

    def log_database_operation(self, operation: str, table: str,
                              execution_time: float, affected_rows: int = None):
        """记录数据库操作日志"""

        self.log_operation(
            LogLevel.INFO,
            f"DB_{operation.upper()}",
            execution_time=execution_time,
            table=table,
            affected_rows=affected_rows
        )
```

### 8.2 性能监控

#### 8.2.1 关键指标监控
```python
import time
from functools import wraps
from dataclasses import dataclass
from typing import Dict, List

@dataclass
class PerformanceMetrics:
    """性能指标"""
    operation_name: str
    execution_time: float
    memory_usage: float
    cpu_usage: float
    timestamp: datetime
    success: bool
    error_message: str = None

class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.metrics: List[PerformanceMetrics] = []
        self.performance_logger = logging.getLogger('performance')

    def monitor_operation(self, operation_name: str):
        """操作性能监控装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = self._get_memory_usage()
                start_cpu = self._get_cpu_usage()

                success = True
                error_message = None
                result = None

                try:
                    result = await func(*args, **kwargs)
                except Exception as e:
                    success = False
                    error_message = str(e)
                    raise
                finally:
                    end_time = time.time()
                    execution_time = end_time - start_time

                    metrics = PerformanceMetrics(
                        operation_name=operation_name,
                        execution_time=execution_time,
                        memory_usage=self._get_memory_usage() - start_memory,
                        cpu_usage=self._get_cpu_usage() - start_cpu,
                        timestamp=datetime.now(),
                        success=success,
                        error_message=error_message
                    )

                    self._record_metrics(metrics)

                return result
            return wrapper
        return decorator

    def _record_metrics(self, metrics: PerformanceMetrics):
        """记录性能指标"""
        self.metrics.append(metrics)

        # 记录到日志
        self.performance_logger.info(
            f"Performance: {metrics.operation_name} | "
            f"Time: {metrics.execution_time:.3f}s | "
            f"Memory: {metrics.memory_usage:.2f}MB | "
            f"Success: {metrics.success}",
            extra={'metrics': metrics.__dict__}
        )

        # 如果执行时间过长，记录警告
        if metrics.execution_time > 10:  # 10秒阈值
            self.performance_logger.warning(
                f"Slow operation detected: {metrics.operation_name} "
                f"took {metrics.execution_time:.3f}s"
            )

    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能摘要"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        recent_metrics = [m for m in self.metrics if m.timestamp > cutoff_time]

        if not recent_metrics:
            return {}

        # 按操作分组统计
        operation_stats = {}
        for metric in recent_metrics:
            op_name = metric.operation_name
            if op_name not in operation_stats:
                operation_stats[op_name] = {
                    'count': 0,
                    'total_time': 0,
                    'success_count': 0,
                    'error_count': 0,
                    'avg_time': 0,
                    'max_time': 0
                }

            stats = operation_stats[op_name]
            stats['count'] += 1
            stats['total_time'] += metric.execution_time
            stats['max_time'] = max(stats['max_time'], metric.execution_time)

            if metric.success:
                stats['success_count'] += 1
            else:
                stats['error_count'] += 1

        # 计算平均值和成功率
        for stats in operation_stats.values():
            stats['avg_time'] = stats['total_time'] / stats['count']
            stats['success_rate'] = stats['success_count'] / stats['count'] * 100

        return {
            'time_range_hours': hours,
            'total_operations': len(recent_metrics),
            'operation_statistics': operation_stats,
            'overall_success_rate': sum(1 for m in recent_metrics if m.success) / len(recent_metrics) * 100
        }
```

### 8.3 告警和通知机制

#### 8.3.1 告警规则配置
```python
class AlertRule:
    """告警规则"""

    def __init__(self, name: str, condition: callable,
                 severity: str, notification_channels: List[str]):
        self.name = name
        self.condition = condition
        self.severity = severity  # 'low', 'medium', 'high', 'critical'
        self.notification_channels = notification_channels

class AlertManager:
    """告警管理器"""

    def __init__(self):
        self.rules = self._setup_alert_rules()
        self.alert_logger = logging.getLogger('alerts')

    def _setup_alert_rules(self) -> List[AlertRule]:
        """设置告警规则"""
        return [
            AlertRule(
                name="High Error Rate",
                condition=lambda metrics: self._check_error_rate(metrics) > 0.1,
                severity="high",
                notification_channels=["email", "slack"]
            ),
            AlertRule(
                name="Slow LLM Generation",
                condition=lambda metrics: self._check_llm_performance(metrics) > 30,
                severity="medium",
                notification_channels=["slack"]
            ),
            AlertRule(
                name="Database Connection Issues",
                condition=lambda metrics: self._check_db_errors(metrics) > 0,
                severity="critical",
                notification_channels=["email", "slack", "sms"]
            ),
            AlertRule(
                name="Authentication Failures",
                condition=lambda metrics: self._check_auth_failures(metrics) > 5,
                severity="high",
                notification_channels=["email", "slack"]
            )
        ]

    def check_alerts(self, performance_summary: Dict[str, Any]):
        """检查告警条件"""
        for rule in self.rules:
            if rule.condition(performance_summary):
                self._trigger_alert(rule, performance_summary)

    def _trigger_alert(self, rule: AlertRule, context: Dict[str, Any]):
        """触发告警"""
        alert_message = f"Alert: {rule.name} | Severity: {rule.severity}"

        self.alert_logger.error(
            alert_message,
            extra={
                'alert_rule': rule.name,
                'severity': rule.severity,
                'context': context
            }
        )

        # 发送通知
        for channel in rule.notification_channels:
            self._send_notification(channel, rule, context)
```

## 9. 开发里程碑和时间规划（Phase 1）

### 9.1 项目阶段划分

#### **Phase 1: 病程记录自动化处理 (当前阶段)**
**目标**: 实现首次病程记录和主治医师查房记录的自动化处理
**时间**: 8周

### 9.2 详细开发计划

#### **第一阶段：基础架构和MCP服务框架 (2周)**
- [ ] **Week 1**:
  - [ ] 项目结构搭建和MCP服务框架设计
  - [ ] PostgreSQL数据库设计和初始化
  - [ ] 认证MCP服务开发
  - [ ] 基础HTTP客户端封装
- [ ] **Week 2**:
  - [ ] 患者信息MCP服务开发
  - [ ] 数据库ORM模型设计
  - [ ] 基础日志和监控系统
  - [ ] 配置管理系统

#### **第二阶段：病程记录模板系统 (2周)**
- [ ] **Week 3**:
  - [ ] 病程记录模板类库设计
  - [ ] FC Code解析器开发
  - [ ] 首次病程记录模板实现
  - [ ] 主治医师查房模板实现
- [ ] **Week 4**:
  - [ ] 模板处理MCP服务开发
  - [ ] XML模板解析和处理
  - [ ] 模板验证和质量控制
  - [ ] 模板缓存机制

#### **第三阶段：LLM集成和内容生成 (2周)**
- [ ] **Week 5**:
  - [ ] LLM客户端集成
  - [ ] 提示词模板系统开发
  - [ ] 首次病程记录内容生成
  - [ ] 时效性约束机制实现
- [ ] **Week 6**:
  - [ ] 查房记录内容生成
  - [ ] 内容质量评估系统
  - [ ] 上下文管理器开发
  - [ ] 生成内容验证机制

#### **第四阶段：病历管理和AI Agent工作流 (1.5周)**
- [ ] **Week 7**:
  - [ ] 病历管理MCP服务开发
  - [ ] AI Agent工作流程实现
  - [ ] 患者查询流程优化
  - [ ] 病历保存流程实现
- [ ] **Week 7.5**:
  - [ ] 完整工作流集成测试
  - [ ] 错误处理和异常恢复
  - [ ] 性能优化

#### **第五阶段：测试、部署和文档 (0.5周)**
- [ ] **Week 8**:
  - [ ] 系统集成测试
  - [ ] 用户接受测试
  - [ ] 部署脚本和文档
  - [ ] 培训材料准备

### 9.3 关键里程碑

| 里程碑 | 时间点 | 交付物 | 验收标准 |
|--------|--------|--------|----------|
| M1: 基础架构完成 | Week 2 | MCP服务框架、数据库、认证服务 | 认证流程正常，数据库连接稳定 |
| M2: 模板系统完成 | Week 4 | 病程记录模板类库、FC Code解析器 | 能够解析和处理病程记录模板 |
| M3: LLM集成完成 | Week 6 | LLM内容生成、质量控制系统 | 能够生成符合质量标准的病程记录 |
| M4: 完整流程实现 | Week 7.5 | AI Agent工作流、病历管理 | 完整的从输入到保存的自动化流程 |
| M5: 项目交付 | Week 8 | 完整系统、文档、培训材料 | 系统稳定运行，满足业务需求 |

### 9.4 资源分配

#### **开发团队配置**:
- **架构师** (1人): 负责MCP服务架构设计和技术决策
- **后端开发** (2人): 负责MCP服务开发和数据库设计
- **AI工程师** (1人): 负责LLM集成和提示词优化
- **测试工程师** (1人): 负责测试用例设计和质量保证
- **DevOps工程师** (0.5人): 负责部署和运维支持

#### **技术栈确认**:
- **MCP框架**: Model Context Protocol
- **数据库**: PostgreSQL 14+
- **ORM**: SQLAlchemy 2.0+
- **LLM**: OpenAI GPT-4 或本地模型
- **监控**: 自研日志系统 + Prometheus (可选)

## 10. 风险评估和缓解策略

### 10.1 技术风险

#### 10.1.1 MCP服务架构风险
**风险**: MCP服务间通信复杂度和稳定性问题
**缓解策略**:
- 实现服务健康检查和自动重启
- 建立服务降级和熔断机制
- 完善的错误处理和重试逻辑

#### 10.1.2 LLM生成质量风险
**风险**: AI生成的病程记录质量不稳定
**缓解策略**:
- 建立多层质量验证机制
- 实现人工审核工作流
- 持续优化提示词和上下文

#### 10.1.3 数据库性能风险
**风险**: 随着数据量增长，查询性能下降
**缓解策略**:
- 合理设计数据库索引
- 实现数据分区和归档策略
- 建立数据库监控和优化机制

### 10.2 业务风险

#### 10.2.1 医疗合规风险
**风险**: 生成的病历内容不符合医疗规范
**缓解策略**:
- 严格的内容验证规则
- 医疗专家参与提示词设计
- 建立医疗质控审核流程

#### 10.2.2 时效性风险
**风险**: 系统无法满足医疗记录的时效性要求
**缓解策略**:
- 实现智能优先级调度
- 建立时效性监控和告警
- 提供紧急情况下的快速通道

### 10.3 运维风险

#### 10.3.1 系统可用性风险
**风险**: 系统故障影响医疗工作流程
**缓解策略**:
- 实现高可用部署架构
- 建立完善的备份和恢复机制
- 提供离线模式和手动备选方案

## 8. 质量保证措施

### 8.1 代码质量
- 遵循PEP 8编码规范
- 实现完整的单元测试覆盖
- 使用类型注解提高代码可读性
- 定期代码审查

### 8.2 系统可靠性
- 实现完善的错误处理机制
- 建立系统监控和告警
- 提供详细的操作日志
- 支持系统状态检查

### 8.3 用户体验
- 提供清晰的操作界面
- 实现进度提示功能
- 支持操作撤销和重试
- 提供详细的帮助文档

## 9. 部署和维护

### 9.1 部署要求
- Python 3.8+ 运行环境
- 网络连接到医院HIS系统
- LLM API访问权限
- 足够的存储空间

### 9.2 维护计划
- 定期更新依赖包
- 监控系统运行状态
- 备份重要配置数据
- 用户培训和技术支持

## 10. 技术实现细节

### 10.1 项目结构
```
medical_record_automation/
├── src/
│   ├── auth/
│   │   ├── __init__.py
│   │   ├── oauth_client.py      # OAuth2认证客户端
│   │   └── token_manager.py     # Token管理器
│   ├── templates/
│   │   ├── __init__.py
│   │   ├── template_manager.py  # 模板管理器
│   │   ├── xml_parser.py        # XML解析器
│   │   └── fc_code_mapper.py    # FC Code映射器
│   ├── patients/
│   │   ├── __init__.py
│   │   ├── patient_manager.py   # 患者信息管理
│   │   └── patient_models.py    # 患者数据模型
│   ├── records/
│   │   ├── __init__.py
│   │   ├── record_processor.py  # 病历处理器
│   │   ├── xml_generator.py     # XML生成器
│   │   └── record_models.py     # 病历数据模型
│   ├── llm/
│   │   ├── __init__.py
│   │   ├── llm_client.py        # LLM客户端
│   │   ├── prompt_templates.py  # 提示词模板
│   │   └── content_generator.py # 内容生成器
│   ├── utils/
│   │   ├── __init__.py
│   │   ├── http_client.py       # HTTP客户端封装
│   │   ├── config.py            # 配置管理
│   │   ├── logger.py            # 日志管理
│   │   └── exceptions.py        # 自定义异常
│   └── main.py                  # 主程序入口
├── tests/
│   ├── test_auth/
│   ├── test_templates/
│   ├── test_patients/
│   ├── test_records/
│   └── test_llm/
├── config/
│   ├── settings.yaml            # 系统配置
│   ├── fc_code_mapping.json     # FC Code映射配置
│   └── prompt_templates.yaml    # 提示词模板配置
├── docs/
│   ├── api_reference.md         # API参考文档
│   ├── user_guide.md            # 用户指南
│   └── development_guide.md     # 开发指南
├── requirements.txt             # 依赖包列表
├── setup.py                     # 安装脚本
└── README.md                    # 项目说明
```

### 10.2 核心类设计

#### 10.2.1 认证管理器
```python
class AuthManager:
    """认证管理器，处理OAuth2认证和Token管理"""

    def __init__(self, base_url: str, client_id: str, client_secret: str):
        self.base_url = base_url
        self.client_id = client_id
        self.client_secret = client_secret
        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = None

    async def authenticate_client(self) -> str:
        """客户端认证，获取访问令牌"""
        pass

    async def authenticate_user(self, user_id: str) -> dict:
        """用户认证，获取用户令牌"""
        pass

    async def refresh_access_token(self) -> str:
        """刷新访问令牌"""
        pass

    def is_token_valid(self) -> bool:
        """检查令牌是否有效"""
        pass
```

#### 10.2.2 模板管理器
```python
class TemplateManager:
    """病历模板管理器"""

    def __init__(self, http_client: HttpClient):
        self.http_client = http_client
        self.template_cache = {}

    async def get_template_list(self, dept_code: str, doc_type: int, template_type: int) -> List[Template]:
        """获取模板列表"""
        pass

    async def get_template_content(self, file_id: str) -> str:
        """获取模板XML内容"""
        pass

    def parse_template_fields(self, xml_content: str) -> List[TemplateField]:
        """解析模板中的可编辑字段"""
        pass
```

#### 10.2.3 LLM集成器
```python
class LLMIntegrator:
    """LLM集成器，处理AI内容生成"""

    def __init__(self, llm_client: LLMClient, prompt_manager: PromptManager):
        self.llm_client = llm_client
        self.prompt_manager = prompt_manager

    async def generate_content(self, field_code: str, context: dict) -> str:
        """根据字段代码和上下文生成内容"""
        pass

    def build_prompt(self, template_name: str, context: dict) -> str:
        """构建提示词"""
        pass

    async def validate_content(self, content: str, field_type: str) -> bool:
        """验证生成内容的质量"""
        pass
```

### 10.3 配置文件示例

#### 10.3.1 系统配置 (settings.yaml)
```yaml
# 服务器配置
server:
  base_url: "http://11.0.10.25:4431"
  timeout: 30
  max_retries: 3

# 认证配置
auth:
  client_id: "ekinghis-doctor-emr"
  client_secret: "123"
  token_refresh_threshold: 300  # 提前5分钟刷新

# LLM配置
llm:
  provider: "openai"  # openai, azure, local
  model: "gpt-4"
  api_key: "${LLM_API_KEY}"
  max_tokens: 2000
  temperature: 0.3

# 缓存配置
cache:
  template_cache_ttl: 3600  # 1小时
  patient_cache_ttl: 1800   # 30分钟

# 日志配置
logging:
  level: "INFO"
  file: "logs/medical_record_automation.log"
  max_size: "10MB"
  backup_count: 5
```

#### 10.3.2 FC Code映射配置 (fc_code_mapping.json)
```json
{
  "patient_info": {
    "FC0000001099": {
      "field_name": "患者姓名",
      "data_type": "string",
      "required": true,
      "source": "patient.name"
    },
    "FC0000001108": {
      "field_name": "性别代码",
      "data_type": "string",
      "required": true,
      "source": "patient.gender",
      "mapping": {"男": "M", "女": "F"}
    },
    "FC0000001105": {
      "field_name": "年龄(岁)",
      "data_type": "number",
      "required": true,
      "source": "patient.age"
    }
  },
  "medical_content": {
    "FC0000001113": {
      "field_name": "主诉",
      "data_type": "string",
      "required": true,
      "llm_template": "chief_complaint",
      "max_length": 200
    },
    "FC0000000850": {
      "field_name": "现病史内容",
      "data_type": "text",
      "required": true,
      "llm_template": "present_illness",
      "max_length": 2000
    }
  }
}
```

#### 10.3.3 提示词模板配置 (prompt_templates.yaml)
```yaml
templates:
  chief_complaint:
    name: "主诉生成"
    prompt: |
      根据以下患者信息生成合适的主诉：
      - 患者：{patient_name}，{age}岁，{gender}
      - 诊断：{diagnosis}
      - 科室：{department}

      要求：
      1. 主诉应简洁明了，突出主要症状
      2. 包含症状持续时间
      3. 符合医学表达习惯
      4. 字数控制在50字以内

      主诉：

  present_illness:
    name: "现病史生成"
    prompt: |
      根据以下信息生成详细的现病史：
      - 患者：{patient_name}，{age}岁，{gender}
      - 主诉：{chief_complaint}
      - 诊断：{diagnosis}
      - 入院方式：{admission_type}

      要求：
      1. 详细描述症状的发生、发展过程
      2. 包含相关的阴性症状
      3. 描述就诊经过和治疗情况
      4. 符合病史书写规范
      5. 字数控制在500字以内

      现病史：

  ward_round_record:
    name: "查房记录生成"
    prompt: |
      生成主治医师查房记录：
      - 患者：{patient_name}，{age}岁，{gender}
      - 住院号：{admission_no}
      - 诊断：{diagnosis}
      - 入院天数：{admission_days}天
      - 当前治疗：{current_treatment}

      要求：
      1. 包含患者当前病情评估
      2. 描述治疗效果和病情变化
      3. 提出下一步治疗计划
      4. 符合查房记录格式
      5. 字数控制在300字以内

      查房记录：
```

### 10.4 错误处理和异常管理

#### 10.4.1 自定义异常类
```python
class MedicalRecordException(Exception):
    """医疗病历系统基础异常"""
    pass

class AuthenticationError(MedicalRecordException):
    """认证错误"""
    pass

class TemplateNotFoundError(MedicalRecordException):
    """模板未找到错误"""
    pass

class PatientNotFoundError(MedicalRecordException):
    """患者未找到错误"""
    pass

class LLMGenerationError(MedicalRecordException):
    """LLM生成错误"""
    pass

class XMLParsingError(MedicalRecordException):
    """XML解析错误"""
    pass
```

#### 10.4.2 重试机制
```python
import asyncio
from functools import wraps

def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    await asyncio.sleep(delay * (2 ** attempt))
            return None
        return wrapper
    return decorator
```

### 10.5 性能优化策略

#### 10.5.1 缓存机制
- **模板缓存**: 缓存常用病历模板，减少网络请求
- **患者信息缓存**: 临时缓存患者信息，避免重复查询
- **LLM响应缓存**: 缓存相似请求的LLM响应

#### 10.5.2 并发处理
- 使用异步编程提高I/O效率
- 实现连接池管理HTTP连接
- 支持批量处理多个病历

#### 10.5.3 资源管理
- 实现内存使用监控
- 定期清理过期缓存
- 优化XML解析性能

### 10.6 安全措施

#### 10.6.1 数据加密
- 传输层使用HTTPS加密
- 敏感配置信息加密存储
- 患者信息脱敏处理

#### 10.6.2 访问控制
- 实现基于角色的权限控制
- 记录所有操作日志
- 定期审计系统访问

#### 10.6.3 数据完整性
- 实现数据校验机制
- 支持操作回滚
- 建立数据备份策略

## 11. 质量保证和部署方案

### 11.1 测试策略

#### 11.1.1 单元测试
```python
# 测试覆盖率要求
UNIT_TEST_REQUIREMENTS = {
    'mcp_services': 90,      # MCP服务测试覆盖率
    'template_classes': 95,   # 模板类测试覆盖率
    'database_models': 85,    # 数据库模型测试覆盖率
    'llm_integration': 80,    # LLM集成测试覆盖率
    'overall_target': 85      # 整体目标覆盖率
}

# 关键测试用例
- MCP服务间通信测试
- 病程记录模板解析测试
- FC Code映射准确性测试
- LLM内容生成质量测试
- 数据库操作完整性测试
- 时效性约束验证测试
```

#### 11.1.2 集成测试
```python
# 端到端测试场景
E2E_TEST_SCENARIOS = [
    {
        'name': '首次病程记录完整流程',
        'steps': [
            '输入住院号',
            '查询患者信息',
            '获取病历列表',
            '生成首次病程记录',
            '验证内容质量',
            '保存到系统'
        ],
        'expected_duration': '< 30秒'
    },
    {
        'name': '主治医师查房记录流程',
        'steps': [
            '检查时效性约束',
            '分析病情变化',
            '生成查房记录',
            '质量评估',
            '保存和提交'
        ],
        'expected_duration': '< 20秒'
    }
]
```

### 11.2 部署架构

#### 11.2.1 生产环境部署
```yaml
# docker-compose.yml
version: '3.8'
services:
  auth-mcp-service:
    image: medical-system/auth-mcp:latest
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=************************************/medical_db
    depends_on:
      - postgres
      - redis

  patient-mcp-service:
    image: medical-system/patient-mcp:latest
    ports:
      - "8002:8000"
    depends_on:
      - auth-mcp-service
      - postgres

  record-mcp-service:
    image: medical-system/record-mcp:latest
    ports:
      - "8003:8000"
    depends_on:
      - auth-mcp-service
      - postgres

  template-mcp-service:
    image: medical-system/template-mcp:latest
    ports:
      - "8004:8000"
    depends_on:
      - auth-mcp-service
      - postgres

  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: medical_db
      POSTGRES_USER: medical_user
      POSTGRES_PASSWORD: secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

#### 11.2.2 监控和告警部署
```yaml
# monitoring-stack.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

  alertmanager:
    image: prom/alertmanager:latest
    ports:
      - "9093:9093"
    volumes:
      - ./alertmanager.yml:/etc/alertmanager/alertmanager.yml

volumes:
  grafana_data:
```

### 11.3 运维手册

#### 11.3.1 日常运维检查清单
```markdown
## 每日检查项目
- [ ] 检查所有MCP服务状态
- [ ] 查看错误日志和告警
- [ ] 验证数据库连接和性能
- [ ] 检查LLM服务可用性
- [ ] 确认备份任务执行状态

## 每周检查项目
- [ ] 分析系统性能趋势
- [ ] 检查磁盘空间使用情况
- [ ] 更新安全补丁
- [ ] 验证备份恢复流程
- [ ] 检查用户反馈和问题

## 每月检查项目
- [ ] 系统容量规划评估
- [ ] 安全审计和漏洞扫描
- [ ] 性能优化建议
- [ ] 灾难恢复演练
- [ ] 文档更新和维护
```

#### 11.3.2 故障处理流程
```python
# 故障分级和响应时间
INCIDENT_LEVELS = {
    'P1_CRITICAL': {
        'description': '系统完全不可用',
        'response_time': '15分钟',
        'resolution_time': '2小时',
        'escalation': ['技术负责人', 'CTO']
    },
    'P2_HIGH': {
        'description': '核心功能受影响',
        'response_time': '30分钟',
        'resolution_time': '4小时',
        'escalation': ['技术负责人']
    },
    'P3_MEDIUM': {
        'description': '部分功能异常',
        'response_time': '2小时',
        'resolution_time': '24小时',
        'escalation': ['开发团队']
    },
    'P4_LOW': {
        'description': '轻微问题或改进',
        'response_time': '1个工作日',
        'resolution_time': '1周',
        'escalation': ['开发团队']
    }
}
```

## 12. 项目总结和后续规划

### 12.1 Phase 1 交付成果

#### **核心功能交付**:
1. ✅ **MCP微服务架构**: 4个独立的MCP服务，支持病程记录自动化处理
2. ✅ **病程记录模板系统**: 首次病程记录和主治医师查房记录模板类
3. ✅ **AI Agent工作流**: 从住院号输入到病历保存的完整自动化流程
4. ✅ **PostgreSQL数据存储**: 完整的数据模型和存储方案
5. ✅ **LLM内容生成**: 智能病程记录内容生成和质量控制
6. ✅ **监控和日志系统**: 完善的系统监控和操作日志记录

#### **技术架构优势**:
- **模块化设计**: MCP服务架构支持独立部署和扩展
- **类型安全**: 完整的病历模板类继承体系
- **质量保证**: 多层次的内容验证和质量控制机制
- **可观测性**: 全面的日志记录和性能监控
- **可扩展性**: 为后续功能扩展预留了良好的架构基础

### 12.2 后续阶段规划

#### **Phase 2: 入院出院记录处理 (预计4周)**
- 扩展模板类库支持入院记录和出院记录
- 增加更复杂的医疗逻辑处理
- 实现多科室模板适配

#### **Phase 3: 高级功能和集成 (预计6周)**
- 实现医疗影像和检查结果的智能分析
- 增加多用户协作和权限管理
- 集成医院现有的HIS系统

#### **Phase 4: 智能化升级 (预计8周)**
- 实现基于历史数据的智能推荐
- 增加自然语言交互界面
- 建立医疗知识图谱集成

### 12.3 成功指标

#### **技术指标**:
- 系统可用性 > 99.5%
- 病程记录生成时间 < 30秒
- 内容质量评分 > 85分
- 错误率 < 1%

#### **业务指标**:
- 医生工作效率提升 > 40%
- 病历完成及时率 > 95%
- 用户满意度 > 4.5/5.0
- 医疗合规性 100%

---

**文档版本**: v2.0
**创建日期**: 2025-08-19
**最后更新**: 2025-08-19
**当前阶段**: Phase 1 - 病程记录自动化处理
**负责人**: 开发团队
**审核人**: 技术架构师、医疗专家
