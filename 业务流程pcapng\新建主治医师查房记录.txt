0.035159s
GET /oss/file/show?fid=11%2C5d0bc48625e5b2 HTTP/1.1
ek-b-clientversion: 1.3.6.399
ek-b-clientip: ***********
ek-b-logintype: 
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jA_u9aw-SmIDxfZ6dh4v9Xpzz7Bl7DQFeiBZ0RYWXBp3QGcDJYAyWpPw4uUQxaLlB4jWPJa_ZNK5sjLCvw8_O_1l3f6-z7Pn3AkyonEy-EC7X3W9w4ZUl4xiPhit53XpLMaSP8GvKwlUpE9-z0sdgeIsdsnXzy-saOLX-uNJj8s
Host: **********:4431


0.014351s
HTTP/1.1 200 
Server: nginx
Date: Sun, 03 Aug 2025 09:48:42 GMT
Content-Type: text/xml;charset=utf-8
Transfer-Encoding: chunked
Connection: keep-alive
serial: gateway_8856188ecfd34f0c99b5143084b6b30e
Last-Modified: 1737124428000
Content-Disposition: inline; filename="1c4cf2b83c8c477996b87e09b4ccdcf1.xml"
serial: gateway_8856188ecfd34f0c99b5143084b6b30e
Accept-Ranges: bytes
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Pragma: no-cache
Expires: 0

32d7
﻿<?xml version="1.0" encoding="utf-8"?>
<XTextDocument xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" EditorVersionString="1.2022.6.27">
   <InnerID>77</InnerID>
   <XElements>
      <Element xsi:type="XTextHeader">
         <InnerID>64</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="XParagraphFlag" StyleIndex="0">
               <InnerID>70</InnerID>
               <AutoCreate>true</AutoCreate>
            </Element>
         </XElements>
      </Element>
      <Element xsi:type="XTextBody">
         <InnerID>65</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="XInputField" StyleIndex="1">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>查房日期时间</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001145</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>DT</Value>
                  </Attribute>
                  <Attribute>
                     <Name>ShowTimeFlag</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>77</InnerID>
               <ID>field1</ID>
               <ToolTip>【查房日期时间】开始查房时的公元纪年日期和时间的完整描述</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding>
                  <DataSource>TI00000210</DataSource>
                  <BindingPath>CI00002256</BindingPath>
               </ValueBinding>
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <Name>FC0000001145</Name>
               <DisplayFormat>
                  <Style>DateTime</Style>
                  <Format>yyyy年MM月dd日 HH时mm分</Format>
               </DisplayFormat>
               <BackgroundText>yyyy年MM月dd日 HH时mm分</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings>
                  <EditStyle>DateTime</EditStyle>
               </FieldSettings>
            </Element>
            <Element xsi:type="XString" WhitespaceCount="7">
               <InnerID>84</InnerID>
               <Text>       </Text>
            </Element>
            <Element xsi:type="XString" StyleIndex="3" WhitespaceCount="1">
               <InnerID>85</InnerID>
               <Text> </Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>查房医师</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000009718</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignByDoctorFlag</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>InputModeParam</Name>
                     <Value>{"DataSourceCode":"WardRoundDoctors","FixedFilter":{"Relation":"OR","Filters":[{"FieldCode":"DeptCode","Operator":"EQ","Parameter":"{patient.deptCode}"}]},"Sort":[{"FieldCode":"StaffFlag"}]}</Value>
                  </Attribute>
               </Attributes>
               <InnerID>99</InnerID>
               <ID>field5</ID>
               <ToolTip>【查房医师】</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <UserEditable>false</UserEditable>
               <Name>FC0000009718</Name>
               <BackgroundText>查房医师</BackgroundText>
               <EditorControlTypeName>EkHis.Components.Emr.Winform.View.AsyncInputControlForDCWriter</EditorControlTypeName>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XTextLabelElement" StyleIndex="3">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>查房记录</Value>
                  </Attribute>
               </Attributes>
               <InnerID>626</InnerID>
               <ID>FC0000001144</ID>
               <Deleteable>false</Deleteable>
               <Name>0</Name>
               <Width>350.854462</Width>
               <Height>49.9023361</Height>
               <Text>主治医师查房记录</Text>
            </Element>
            <Element xsi:type="XParagraphFlag">
               <InnerID>633</InnerID>
            </Element>
            <Element xsi:type="XParagraphFlag" StyleIndex="4">
               <InnerID>375</InnerID>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="2">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>主治医师签名</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001159</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignLevel</Name>
                     <Value>EMR_AUDIT_ATTEND</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignVerify</Name>
                     <Value>2</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignByDoctorFlag</Name>
                     <Value>0</Value>
                  </Attribute>
               </Attributes>
               <InnerID>70</InnerID>
               <ID>field4</ID>
               <ToolTip>【主治医师签名】具有主治医师专业技术职务资格的医师签署的在公安户籍管理部门   式登记注册的姓氏和名称</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <UserEditable>false</UserEditable>
               <Name>FC0000001159</Name>
               <BackgroundText>上级医师签名</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XString" StyleIndex="2">
               <InnerID>86</InnerID>
               <Text>/</Text>
            </Element>
            <Element xsi:type="XInputField" StyleIndex="2">
               <Attributes>
                  <Attribute>
                     <Name>FromType</Name>
                     <Value>emrfield</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldName</Name>
                     <Value>记录人签名</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldCode</Name>
                     <Value>FC0000001147</Value>
                  </Attribute>
                  <Attribute>
                     <Name>FieldDataType</Name>
                     <Value>S1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>IsSignInput</Name>
                     <Value>1</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignLevel</Name>
                     <Value>EMR_AUDIT_RESIDENT</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignVerify</Name>
                     <Value>0</Value>
                  </Attribute>
                  <Attribute>
                     <Name>SignShowType</Name>
                     <Value>1</Value>
                  </Attribute>
               </Attributes>
               <InnerID>62</InnerID>
               <ID>field3</ID>
               <ToolTip>【记录人签名】记录单填写者签署的在公安户籍管理部门正式登记注册的姓氏和名称</ToolTip>
               <EnableValueValidate>true</EnableValueValidate>
               <ValueBinding />
               <Deleteable>false</Deleteable>
               <BorderElementColor />
               <Name>FC0000001147</Name>
               <BackgroundText>双击签名</BackgroundText>
               <EditorActiveMode>MouseClick</EditorActiveMode>
               <FieldSettings />
            </Element>
            <Element xsi:type="XParagraphFlag" StyleIndex="2">
               <InnerID>69</InnerID>
            </Element>
         </XElements>
      </Element>
      <Element xsi:type="XTextFooter">
         <InnerID>66</InnerID>
         <AcceptTab>true</AcceptTab>
         <XElements>
            <Element xsi:type="XParagraphFlag">
               <InnerID>71</InnerID>
               <AutoCreate>true</AutoCreate>
            </Element>
         </XElements>
      </Element>
   </XElements>
   <FileName>D:\ekingsoft\outpdoct1 - 管理员\ekingemr\tempdoc\1c4cf2b83c8c477996b87e09b4ccdcf1.xml</FileName>
   <ContentStyles>
      <Default xsi:type="DocumentContentStyle">
         <FontName>宋体</FontName>
         <FontSize>10.5</FontSize>
      </Default>
      <Styles>
         <Style Index="0">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <Align>Center</Align>
         </Style>
         <Style Index="1">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
         </Style>
         <Style Index="2">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <Align>Right</Align>
         </Style>
         <Style Index="3">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
            <Bold>true</Bold>
         </Style>
         <Style Index="4">
            <FontName>宋体</FontName>
            <FontSize>10.5</FontSize>
         </Style>
      </Styles>
   </ContentStyles>
   <Info>
      <LicenseText>江苏鑫亿软件股份有限公司:鹤壁市人民医院</LicenseText>
      <CreationTime>1980-01-01T00:00:00</CreationTime>
      <LastModifiedTime>2025-01-17T22:33:48.267+08:00</LastModifiedTime>
      <LastPrintTime>1980-01-01T00:00:00</LastPrintTime>
      <Operator>DCSoft.Writer Version:1.2022.6.27</Operator>
      <NumOfPage>1</NumOfPage>
   </Info>
   <BodyText>        主治医师查房记录

/</BodyText>
   <LocalConfig />
   <PageSettings>
      <StrictUsePageSize>false</StrictUsePageSize>
   </PageSettings>
</XTextDocument>
0


0.191390s
GET /emr/catalogue/emrDataInfo/processTimeStamp?clCode=********** HTTP/1.1
ek-b-clientversion: 1.3.6.399
ek-b-clientip: ***********
ek-b-logintype: 
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jA_u9aw-SmIDxfZ6dh4v9Xpzz7Bl7DQFeiBZ0RYWXBp3QGcDJYAyWpPw4uUQxaLlB4jWPJa_ZNK5sjLCvw8_O_1l3f6-z7Pn3AkyonEy-EC7X3W9w4ZUl4xiPhit53XpLMaSP8GvKwlUpE9-z0sdgeIsdsnXzy-saOLX-uNJj8s
Host: **********:4431


0.038245s
HTTP/1.1 200 
Server: nginx
Date: Sun, 03 Aug 2025 09:48:42 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
serial: gateway_69fb06eaf0c44692abe032f03a084115
serial: gateway_69fb06eaf0c44692abe032f03a084115
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Pragma: no-cache
Expires: 0

80
{"code":"SUCCESS","data":{"dataInfo":1745221608613},"serial":"gateway_69fb06eaf0c44692abe032f03a084115","sucMsg":"操作成功"}
0


0.004160s
GET /manager/range-content-dict/itemTypeCodes?itemTypeCodes=DICT_12904%2C%2C%2C%2C%2C%2C%2C%2C%2C HTTP/1.1
ek-b-clientversion: 1.3.6.399
ek-b-clientip: ***********
ek-b-logintype: 
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jA_u9aw-SmIDxfZ6dh4v9Xpzz7Bl7DQFeiBZ0RYWXBp3QGcDJYAyWpPw4uUQxaLlB4jWPJa_ZNK5sjLCvw8_O_1l3f6-z7Pn3AkyonEy-EC7X3W9w4ZUl4xiPhit53XpLMaSP8GvKwlUpE9-z0sdgeIsdsnXzy-saOLX-uNJj8s
Host: **********:4431


0.016510s
HTTP/1.1 200 
Server: nginx
Date: Sun, 03 Aug 2025 09:48:42 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
serial: gateway_f1440040689143f9becf6863d189be20
serial: gateway_f1440040689143f9becf6863d189be20
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Pragma: no-cache
Expires: 0

4f4
{"code":"SUCCESS","data":{"dataInfo":[{"DICT_12904":[{"itemWb":"YWAJ:","lastModifyById":"513362307871408128","itemType":"职称(住院诊断证明)","processTimeStamp":1717487041425,"itemPy":"ZRYS","lastModifyBy":"GLY","editable":0,"itemCode":"1","itemTypeCode":"DICT_12904","itemVersion":0,"lastModifyByClientId":"ekinghis-doctor-emr","editRequired":0,"sortNo":1,"createBy":"GLY","itemName":"主任医师:","tenancyId":-1,"createTime":"2024-01-12 10:32:33","createdByClientId":"ekinghis-doctor-emr","lastModifyTime":"2024-06-04 15:44:01","disabled":false,"id":"943132403793133569","createdById":"513362307871408128"},{"itemWb":"GYWAJ:","lastModifyById":"513362307871408128","itemType":"职称(住院诊断证明)","processTimeStamp":1717487041436,"itemPy":"FZRYS","lastModifyBy":"GLY","editable":0,"itemCode":"2","itemTypeCode":"DICT_12904","itemVersion":0,"lastModifyByClientId":"ekinghis-doctor-emr","editRequired":0,"sortNo":2,"createBy":"GLY","itemName":"副主任医师:","tenancyId":-1,"createTime":"2024-01-12 10:32:33","createdByClientId":"ekinghis-doctor-emr","lastModifyTime":"2024-06-04 15:44:01","disabled":false,"id":"943132403793133568","createdById":"513362307871408128"}]}]},"serial":"gateway_f1440040689143f9becf6863d189be20","sucMsg":"操作成功"}
0


0.014791s
POST /manager/processToken/test_process_data?funCode=GeneralZY&funParams=%7B%22patNo%22%3A%22574690%22%2C%22visitTime%22%3A%221%22%2C%22visit_id%22%3A1148948433462099968%2C%22doc_id%22%3A1148966573852065792%2C%22idNo%22%3A%22410611196409093026%22%7D HTTP/1.1
ek-b-clientversion: 1.3.6.399
ek-b-clientip: ***********
ek-b-logintype: 
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jA_u9aw-SmIDxfZ6dh4v9Xpzz7Bl7DQFeiBZ0RYWXBp3QGcDJYAyWpPw4uUQxaLlB4jWPJa_ZNK5sjLCvw8_O_1l3f6-z7Pn3AkyonEy-EC7X3W9w4ZUl4xiPhit53XpLMaSP8GvKwlUpE9-z0sdgeIsdsnXzy-saOLX-uNJj8s
Host: **********:4431
Content-Length: 0


0.013015s
HTTP/1.1 200 
Server: nginx
Date: Sun, 03 Aug 2025 09:48:43 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
serial: gateway_e988f576f10c4af7af802a031e9471c8
serial: gateway_e988f576f10c4af7af802a031e9471c8
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Pragma: no-cache
Expires: 0

15c
{"code":"SUCCESS","data":{"dataInfo":{"result":[{"CurDate":"2025-08-03","CurDateTime":"2025-08-03 17:48:43","Month":"08","CurDateTime2":"2025-08-03 17:48","Year":"2025","CurTime":"17:48:43","HospitalName":"鹤壁市人民医院","Day":"03"}],"success":true,"message":""}},"serial":"gateway_e988f576f10c4af7af802a031e9471c8","sucMsg":"操作成功"}
0