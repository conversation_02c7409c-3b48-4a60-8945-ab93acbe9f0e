# 医疗病历自动化处理系统依赖包列表
# Medical Record Automation System Requirements

# HTTP客户端和网络请求
httpx>=0.24.0                    # 现代异步HTTP客户端
requests>=2.31.0                 # 传统HTTP客户端（备用）
aiohttp>=3.8.0                   # 异步HTTP客户端（备用）

# XML和数据处理
lxml>=4.9.0                      # XML解析和处理
xmltodict>=0.13.0                # XML转字典工具
beautifulsoup4>=4.12.0           # HTML/XML解析器
defusedxml>=0.7.1                # 安全的XML解析器

# JSON和数据序列化
pydantic>=2.0.0                  # 数据验证和序列化
marshmallow>=3.19.0              # 数据序列化框架
dataclasses-json>=0.5.9          # 数据类JSON序列化

# 配置管理
pyyaml>=6.0                      # YAML配置文件解析
python-dotenv>=1.0.0             # 环境变量管理
configparser>=5.3.0              # INI配置文件解析

# 日志和监控
loguru>=0.7.0                    # 现代日志库
structlog>=23.1.0                # 结构化日志
prometheus-client>=0.17.0        # 监控指标

# 缓存和存储
redis>=4.5.0                     # Redis客户端
diskcache>=5.6.0                 # 磁盘缓存
cachetools>=5.3.0                # 内存缓存工具

# 加密和安全
cryptography>=41.0.0             # 加密库
pyjwt>=2.8.0                     # JWT处理
bcrypt>=4.0.0                    # 密码哈希

# 异步编程
asyncio>=3.4.3                   # 异步编程框架
aiofiles>=23.1.0                 # 异步文件操作
asyncio-throttle>=1.0.2          # 异步限流

# 数据库（可选）
sqlalchemy>=2.0.0                # ORM框架
alembic>=1.11.0                  # 数据库迁移
sqlite3                          # SQLite数据库（内置）

# LLM和AI集成
openai>=1.0.0                    # OpenAI API客户端
anthropic>=0.3.0                 # Anthropic Claude API
azure-openai>=1.0.0              # Azure OpenAI服务
transformers>=4.30.0             # Hugging Face模型库
torch>=2.0.0                     # PyTorch（本地模型）
tiktoken>=0.4.0                  # Token计数工具

# 文本处理
jieba>=0.42.1                    # 中文分词
pypinyin>=0.49.0                 # 中文拼音转换
regex>=2023.6.3                  # 正则表达式增强

# 时间和日期处理
python-dateutil>=2.8.2           # 日期时间工具
pytz>=2023.3                     # 时区处理
arrow>=1.2.3                     # 现代日期时间库

# 命令行界面
click>=8.1.0                     # 命令行框架
rich>=13.4.0                     # 富文本终端输出
typer>=0.9.0                     # 现代CLI框架
tqdm>=4.65.0                     # 进度条

# 图形界面（可选）
tkinter                          # GUI框架（内置）
PyQt5>=5.15.0                    # Qt GUI框架
streamlit>=1.25.0                # Web应用框架

# 测试框架
pytest>=7.4.0                    # 测试框架
pytest-asyncio>=0.21.0           # 异步测试
pytest-cov>=4.1.0                # 测试覆盖率
pytest-mock>=3.11.0              # 模拟对象
httpx-mock>=0.10.0               # HTTP模拟

# 代码质量
black>=23.7.0                    # 代码格式化
isort>=5.12.0                    # 导入排序
flake8>=6.0.0                    # 代码检查
mypy>=1.5.0                      # 类型检查
pre-commit>=3.3.0                # Git钩子

# 文档生成
sphinx>=7.1.0                    # 文档生成
mkdocs>=1.5.0                    # Markdown文档
mkdocs-material>=9.1.0           # Material主题

# 性能分析
memory-profiler>=0.60.0          # 内存分析
line-profiler>=4.0.0             # 行级性能分析
py-spy>=0.3.14                   # 性能监控

# 工具库
python-magic>=0.4.27             # 文件类型检测
pillow>=10.0.0                   # 图像处理
qrcode>=7.4.2                    # 二维码生成
barcode>=1.0.0                   # 条形码生成

# 网络和通信
websockets>=11.0.0               # WebSocket客户端
paho-mqtt>=1.6.0                 # MQTT客户端
paramiko>=3.2.0                  # SSH客户端

# 数据分析（可选）
pandas>=2.0.0                    # 数据分析
numpy>=1.24.0                    # 数值计算
matplotlib>=3.7.0                # 数据可视化
seaborn>=0.12.0                  # 统计可视化

# 部署和运维
gunicorn>=21.2.0                 # WSGI服务器
uvicorn>=0.23.0                  # ASGI服务器
docker>=6.1.0                    # Docker客户端
kubernetes>=27.2.0               # Kubernetes客户端

# 开发工具
ipython>=8.14.0                  # 交互式Python
jupyter>=1.0.0                   # Jupyter笔记本
notebook>=7.0.0                  # Jupyter Notebook

# 版本要求说明：
# - 所有版本号都是最低要求版本
# - 建议使用虚拟环境安装依赖
# - 某些包可能需要系统级依赖，请参考各包的安装文档
# - LLM相关包较大，可根据实际需求选择安装

# 安装命令：
# pip install -r requirements.txt

# 开发环境安装（包含测试和开发工具）：
# pip install -r requirements.txt
# pip install -r requirements-dev.txt

# 生产环境安装（仅核心依赖）：
# pip install httpx lxml pydantic pyyaml loguru redis cryptography asyncio
