POST /authserver/oauth/token HTTP/1.1
Authorization: Basic ZWtpbmdoaXMtZG9jdG9yLWVtcjoxMjM=
Content-Type: application/x-www-form-urlencoded
Host: **********:4431
Content-Length: 29
Expect: 100-continue
Connection: Keep-Alive


0.000578s
HTTP/1.1 100 Continue


0.007615s
grant_type=client_credentials
0.187052s
HTTP/1.1 200 
Server: nginx
Date: Tue, 29 Jul 2025 03:34:29 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
serial: gateway_183911d039274ec5a38b28905e992905
serial: gateway_183911d039274ec5a38b28905e992905
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Pragma: no-cache
Expires: 0

308
{"access_token":"*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","token_type":"bearer","expires_in":43199,"scope":"read write internal","tenancyId":null,"roles":null,"userName":null,"userId":null,"jti":"dbd2f78a-1a91-411e-bbc2-20751bcce4ce"}
0

POST /manager/auth-ext/auth HTTP/1.1
Authorization: Bearer *****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Content-Type: application/x-www-form-urlencoded
Host: **********:4431
Content-Length: 84
Expect: 100-continue


0.000808s
HTTP/1.1 100 Continue


0.000172s
authType=ekinghis_login&basic=Basic+ZWtpbmdoaXMtZG9jdG9yLWVtcjoxMjM%3D&cred=24121605
0.094870s
HTTP/1.1 200 
Server: nginx
Date: Tue, 29 Jul 2025 03:34:30 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
serial: gateway_7088f8fbbd9e4417b75673da9e43264d
serial: gateway_7088f8fbbd9e4417b75673da9e43264d
X-Content-Type-Options: nosniff
X-XSS-Protection: 1; mode=block
Cache-Control: no-cache, no-store, max-age=0, must-revalidate
Pragma: no-cache
Expires: 0

6cf
{"code":"SUCCESS","data":{"dataInfo":{"access_token":"*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","refresh_token":"*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","tenancyId":"-1","scope":"read write internal","roles":["ROLE_PEOPLE","ROLE_DOCTOR"],"token_type":"bearer","userName":"24121605","expires_in":43199,"userId":"1066434522704248832","jti":"6fd0d060-ca65-4cd0-b8f5-cdf1c8958877"}},"serial":"gateway_7088f8fbbd9e4417b75673da9e43264d","sucMsg":"操作成功"}
0

