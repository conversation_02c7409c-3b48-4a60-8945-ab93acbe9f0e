# FC Code与XML模板映射机制详细分析

## 1. FC Code的定义和作用机制

### 1.1 FC Code定义
FC Code（Field Code）是医疗信息系统中的**字段编码标准**，用于在XML模板中唯一标识特定的数据字段。它建立了业务逻辑层与表现层之间的桥梁。

### 1.2 FC Code格式规范
```
格式: FC + 10位数字
示例: FC0000001099
```

### 1.3 作用机制
1. **唯一标识**: 每个FC Code对应一个特定的医疗数据字段
2. **跨模板复用**: 同一FC Code在不同模板中表示相同含义的字段
3. **数据绑定**: 将业务数据与XML模板中的具体位置关联
4. **自动填充**: 系统可根据FC Code自动定位并填充相应内容

## 2. FC Code在XML模板结构中的存储位置

### 2.1 主要存储位置

#### 位置1: Attributes/Attribute/Value中的FieldCode
```xml
<Element xsi:type="XInputField">
    <Attributes>
        <Attribute>
            <Name>FieldCode</Name>
            <Value>FC0000001099</Value>  <!-- FC Code存储位置1 -->
        </Attribute>
        <Attribute>
            <Name>FieldName</Name>
            <Value>患者姓名</Value>
        </Attribute>
    </Attributes>
    <Name>FC0000001099</Name>  <!-- FC Code存储位置2 -->
    <InnerValue>程太生</InnerValue>  <!-- 实际数据值 -->
</Element>
```

#### 位置2: 元素的Name属性
```xml
<Element xsi:type="XInputField">
    <Name>FC0000001113</Name>  <!-- FC Code直接作为Name -->
    <InnerValue>认知功能下降半年，加重1周</InnerValue>
</Element>
```

#### 位置3: 元素的ID属性
```xml
<Element xsi:type="XTextLabelElement">
    <ID>FC0000009697</ID>  <!-- FC Code作为ID -->
    <Text>首次病程记录</Text>
</Element>
```

### 2.2 XML元素类型与FC Code关系

| 元素类型 | 用途 | FC Code位置 | 是否可编辑 |
|---------|------|-------------|-----------|
| XInputField | 输入字段 | Attributes + Name | 是 |
| XTextLabelElement | 文本标签 | ID | 否 |
| XString | 文本内容 | 父元素Name | 否 |

## 3. FC Code查找和定位算法

### 3.1 多策略查找算法
```python
def find_element_by_fc_code(root: ET.Element, fc_code: str) -> Optional[ET.Element]:
    """多策略FC Code查找算法"""
    
    # 策略1: 在Attributes中查找FieldCode
    for element in root.iter():
        attributes = element.find('.//Attributes')
        if attributes is not None:
            for attr in attributes.findall('Attribute'):
                name_elem = attr.find('Name')
                value_elem = attr.find('Value')
                if (name_elem is not None and value_elem is not None and 
                    name_elem.text == 'FieldCode' and value_elem.text == fc_code):
                    return element
    
    # 策略2: 在Name属性中查找
    for element in root.iter():
        name_elem = element.find('.//Name')
        if name_elem is not None and name_elem.text == fc_code:
            return element
    
    # 策略3: 在ID属性中查找
    for element in root.iter():
        if element.get('ID') == fc_code:
            return element
    
    return None
```

### 3.2 查找优先级
1. **Attributes/FieldCode** (最高优先级)
2. **Name属性**
3. **ID属性**

## 4. XML标签相关属性和子元素

### 4.1 核心属性结构
```xml
<Element xsi:type="XInputField">
    <!-- 字段定义属性 -->
    <Attributes>
        <Attribute>
            <Name>FieldCode</Name>
            <Value>FC0000001099</Value>  <!-- FC Code -->
        </Attribute>
        <Attribute>
            <Name>FieldName</Name>
            <Value>患者姓名</Value>  <!-- 字段显示名称 -->
        </Attribute>
        <Attribute>
            <Name>FieldDataType</Name>
            <Value>S1</Value>  <!-- 数据类型: S1=字符串, N=数字, DT=日期时间 -->
        </Attribute>
        <Attribute>
            <Name>IsSignInput</Name>
            <Value>0</Value>  <!-- 是否为签名输入: 0=否, 1=是 -->
        </Attribute>
    </Attributes>
    
    <!-- 元素标识 -->
    <InnerID>13</InnerID>
    <ID>field12</ID>
    <Name>FC0000001099</Name>  <!-- FC Code副本 -->
    
    <!-- 数据内容 -->
    <XElements>
        <Element xsi:type="XString">
            <Text>程太生</Text>  <!-- 实际显示的文本 -->
        </Element>
    </XElements>
    
    <!-- 字段配置 -->
    <InnerValue>程太生</InnerValue>  <!-- 字段的实际值 -->
    <BackgroundText>患者姓名</BackgroundText>  <!-- 背景提示文本 -->
    <UserEditable>false</UserEditable>  <!-- 用户是否可编辑 -->
    <ContentReadonly>True</ContentReadonly>  <!-- 内容是否只读 -->
    
    <!-- 显示配置 -->
    <EditorActiveMode>MouseClick</EditorActiveMode>
    <FieldSettings />
</Element>
```

### 4.2 关键子元素说明

| 子元素 | 作用 | 示例值 |
|--------|------|--------|
| `InnerValue` | 存储字段的实际数据值 | "程太生" |
| `BackgroundText` | 字段的提示文本 | "患者姓名" |
| `XElements/XString/Text` | 显示在界面上的文本 | "程太生" |
| `UserEditable` | 控制用户是否可编辑 | "false" |
| `ContentReadonly` | 控制内容是否只读 | "True" |

## 5. FC Code分类和处理策略

### 5.1 FC Code分类体系

#### 5.1.1 患者基本信息类 (FC0000001xxx)
```python
PATIENT_INFO_CODES = {
    "FC0000001099": "患者姓名",
    "FC0000001108": "性别代码", 
    "FC0000001105": "年龄(岁)",
    "FC0000001092": "病床号",
    "FC0000001114": "住院号",
    "FC0000001104": "科室名称"
}
```

**特点:**
- 通常为只读字段 (`UserEditable=false`)
- 数据来源于患者基本信息
- 在多个模板中保持一致

#### 5.1.2 医疗内容类 (FC0000000xxx, FC0000008xxx, FC0000009xxx)
```python
MEDICAL_CONTENT_CODES = {
    "FC0000001113": "主诉",
    "FC0000000850": "现病史内容",
    "FC0000000854": "既往史内容", 
    "FC0000000855": "体格检查内容",
    "FC0000000856": "辅助检查内容",
    "FC0000000939": "入院诊断西医诊断名称",
    "FC0000001101": "鉴别诊断",
    "FC0000010248": "传染病史",
    "FC0000010250": "手术史",
    "FC0000010252": "疾病史(含外伤)",
    "FC0000010253": "输血史", 
    "FC0000010255": "过敏史"
}
```

**特点:**
- 通常为可编辑字段
- 是LLM自动填充的主要目标
- 内容长度变化较大

#### 5.1.3 系统信息类 (FC0000015xxx)
```python
SYSTEM_INFO_CODES = {
    "FC0000015114": "医院名称",
    "FC0000015208": "住院次数", 
    "FC0000015219": "年龄单位"
}
```

**特点:**
- 系统自动填充
- 相对固定的值
- 不需要LLM生成

#### 5.1.4 签名类 (FC0000001xxx)
```python
SIGNATURE_CODES = {
    "FC0000001115": "住院医师签名",
    "FC0000001107": "上级医师审签",
    "FC0000001159": "主治医师签名", 
    "FC0000001147": "记录人签名"
}
```

**特点:**
- 需要用户手动签名或系统自动签名
- 与权限控制相关
- 通常在保存时处理

#### 5.1.5 日期时间类
```python
DATETIME_CODES = {
    "FC0000001100": "记录日期时间",
    "FC0000001145": "查房日期时间"
}
```

**特点:**
- 系统自动生成当前时间
- 有特定的显示格式
- 通常不可编辑

### 5.2 不同类型的处理策略

#### 5.2.1 患者信息类处理
```python
def fill_patient_info(fc_code: str, patient_data: dict) -> str:
    """填充患者基本信息"""
    mapping = {
        "FC0000001099": patient_data.get("name", ""),
        "FC0000001108": patient_data.get("gender", ""),
        "FC0000001105": str(patient_data.get("age", "")),
        "FC0000001092": patient_data.get("bed_no", ""),
        "FC0000001114": patient_data.get("admission_no", ""),
        "FC0000001104": patient_data.get("department", "")
    }
    return mapping.get(fc_code, "")
```

#### 5.2.2 医疗内容类处理
```python
async def fill_medical_content(fc_code: str, context: dict) -> str:
    """使用LLM填充医疗内容"""
    prompt_templates = {
        "FC0000001113": "根据患者{name}，{age}岁{gender}，诊断为{diagnosis}，生成合适的主诉",
        "FC0000000850": "基于主诉：{chief_complaint}，生成详细的现病史",
        # ... 其他模板
    }
    
    if fc_code in prompt_templates:
        prompt = prompt_templates[fc_code].format(**context)
        return await llm_client.generate(prompt)
    return ""
```

#### 5.2.3 系统信息类处理
```python
def fill_system_info(fc_code: str) -> str:
    """填充系统信息"""
    from datetime import datetime
    
    mapping = {
        "FC0000015114": "鹤壁市人民医院",
        "FC0000015208": "1", 
        "FC0000015219": "岁",
        "FC0000001100": datetime.now().strftime("%Y/%m/%d %H:%M:%S")
    }
    return mapping.get(fc_code, "")
```

## 6. 完整的处理流程示例

### 6.1 XML模板处理完整流程
```python
class MedicalRecordProcessor:
    """医疗病历处理器"""
    
    async def process_template(self, xml_content: str, patient_data: dict) -> str:
        """处理XML模板的完整流程"""
        
        # 1. 解析XML，提取所有FC Code字段
        root = ET.fromstring(xml_content)
        fc_fields = self.parse_fc_codes(xml_content)
        
        # 2. 按类型分组处理
        for fc_code, field in fc_fields.items():
            element = self.find_element_by_fc_code(root, fc_code)
            if not element:
                continue
                
            new_value = ""
            
            # 根据字段类型选择处理策略
            if field.category == FCCodeType.PATIENT_INFO:
                new_value = self.fill_patient_info(fc_code, patient_data)
            elif field.category == FCCodeType.MEDICAL_CONTENT and field.is_editable:
                new_value = await self.fill_medical_content(fc_code, patient_data)
            elif field.category == FCCodeType.SYSTEM_INFO:
                new_value = self.fill_system_info(fc_code)
            elif field.category == FCCodeType.DATETIME:
                new_value = self.fill_datetime(fc_code)
            
            # 3. 更新XML元素的值
            if new_value:
                self.update_element_value(element, new_value)
        
        # 4. 返回处理后的XML
        return ET.tostring(root, encoding='unicode')
```

### 6.2 字段值更新策略
```python
def update_element_value(self, element: ET.Element, new_value: str) -> bool:
    """更新XML元素的值"""
    try:
        # 更新InnerValue（数据存储）
        inner_value = element.find('.//InnerValue')
        if inner_value is not None:
            inner_value.text = new_value
        
        # 更新XString/Text（显示内容）
        xstring_text = element.find('.//XElements/Element/Text')
        if xstring_text is not None:
            xstring_text.text = new_value
        
        return True
    except Exception as e:
        print(f"更新字段值失败: {e}")
        return False
```

## 7. 错误处理和边界情况

### 7.1 常见问题和解决方案

1. **FC Code不存在**: 记录警告日志，跳过处理
2. **XML结构异常**: 使用多种查找策略
3. **字段只读**: 检查`UserEditable`和`ContentReadonly`属性
4. **值格式不匹配**: 根据`FieldDataType`进行格式转换

### 7.2 数据验证
```python
def validate_field_value(self, fc_code: str, value: str, field_type: str) -> bool:
    """验证字段值的有效性"""
    if field_type == "N":  # 数字类型
        try:
            float(value)
            return True
        except ValueError:
            return False
    elif field_type == "DT":  # 日期时间类型
        # 验证日期时间格式
        pass
    return True  # 字符串类型默认有效
```

这个详细的FC Code映射机制为医疗病历自动化处理系统提供了精确的字段定位和内容填充能力，确保了系统的可靠性和准确性。
