#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FC Code与XML模板映射机制详解
基于抓包数据分析的完整实现

Author: 开发团队
Date: 2025-08-19
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import re


class FCCodeType(Enum):
    """FC Code类型枚举"""
    PATIENT_INFO = "patient_info"      # 患者基本信息类
    MEDICAL_CONTENT = "medical_content"  # 医疗内容类
    SYSTEM_INFO = "system_info"        # 系统信息类
    SIGNATURE = "signature"            # 签名类
    DATETIME = "datetime"              # 日期时间类


@dataclass
class FCCodeField:
    """FC Code字段信息"""
    fc_code: str                    # FC Code编码
    field_name: str                 # 字段名称
    field_type: str                 # 数据类型
    element_type: str               # XML元素类型
    is_editable: bool              # 是否可编辑
    is_required: bool              # 是否必填
    current_value: str             # 当前值
    background_text: str           # 背景提示文本
    xml_element: ET.Element        # 对应的XML元素
    category: FCCodeType           # 字段分类


class FCCodeMapper:
    """FC Code映射器 - 核心映射逻辑实现"""
    
    def __init__(self):
        # FC Code分类映射表
        self.fc_code_categories = {
            # 患者基本信息类 (FC0000001xxx)
            "FC0000001099": FCCodeType.PATIENT_INFO,  # 患者姓名
            "FC0000001108": FCCodeType.PATIENT_INFO,  # 性别代码
            "FC0000001105": FCCodeType.PATIENT_INFO,  # 年龄(岁)
            "FC0000001092": FCCodeType.PATIENT_INFO,  # 病床号
            "FC0000001114": FCCodeType.PATIENT_INFO,  # 住院号
            "FC0000001104": FCCodeType.PATIENT_INFO,  # 科室名称
            
            # 医疗内容类 (FC0000000xxx, FC0000008xxx, FC0000009xxx)
            "FC0000001113": FCCodeType.MEDICAL_CONTENT,  # 主诉
            "FC0000000850": FCCodeType.MEDICAL_CONTENT,  # 现病史内容
            "FC0000000854": FCCodeType.MEDICAL_CONTENT,  # 既往史内容
            "FC0000000855": FCCodeType.MEDICAL_CONTENT,  # 体格检查内容
            "FC0000000856": FCCodeType.MEDICAL_CONTENT,  # 辅助检查内容
            "FC0000000939": FCCodeType.MEDICAL_CONTENT,  # 入院诊断西医诊断名称
            "FC0000001101": FCCodeType.MEDICAL_CONTENT,  # 鉴别诊断
            "FC0000010248": FCCodeType.MEDICAL_CONTENT,  # 传染病史
            "FC0000010250": FCCodeType.MEDICAL_CONTENT,  # 手术史
            "FC0000010252": FCCodeType.MEDICAL_CONTENT,  # 疾病史(含外伤)
            "FC0000010253": FCCodeType.MEDICAL_CONTENT,  # 输血史
            "FC0000010255": FCCodeType.MEDICAL_CONTENT,  # 过敏史
            
            # 系统信息类 (FC0000015xxx)
            "FC0000015114": FCCodeType.SYSTEM_INFO,   # 医院名称
            "FC0000015208": FCCodeType.SYSTEM_INFO,   # 住院次数
            "FC0000015219": FCCodeType.SYSTEM_INFO,   # 年龄单位
            
            # 签名类 (FC0000001xxx)
            "FC0000001115": FCCodeType.SIGNATURE,     # 住院医师签名
            "FC0000001107": FCCodeType.SIGNATURE,     # 上级医师审签
            "FC0000001159": FCCodeType.SIGNATURE,     # 主治医师签名
            "FC0000001147": FCCodeType.SIGNATURE,     # 记录人签名
            
            # 日期时间类
            "FC0000001100": FCCodeType.DATETIME,      # 记录日期时间
            "FC0000001145": FCCodeType.DATETIME,      # 查房日期时间
        }
    
    def parse_xml_template(self, xml_content: str) -> Dict[str, FCCodeField]:
        """
        解析XML模板，提取所有FC Code字段
        
        Args:
            xml_content: XML模板内容
            
        Returns:
            Dict[str, FCCodeField]: FC Code到字段信息的映射
        """
        try:
            root = ET.fromstring(xml_content)
            fc_fields = {}
            
            # 遍历所有元素，查找包含FC Code的字段
            for element in root.iter():
                fc_field = self._extract_fc_code_from_element(element)
                if fc_field:
                    fc_fields[fc_field.fc_code] = fc_field
            
            return fc_fields
            
        except ET.ParseError as e:
            raise ValueError(f"XML解析错误: {e}")
    
    def _extract_fc_code_from_element(self, element: ET.Element) -> Optional[FCCodeField]:
        """
        从XML元素中提取FC Code信息
        
        Args:
            element: XML元素
            
        Returns:
            Optional[FCCodeField]: FC Code字段信息，如果不包含FC Code则返回None
        """
        fc_code = None
        field_name = None
        field_type = None
        is_editable = True
        current_value = ""
        background_text = ""
        
        # 方法1: 从Attributes中查找FieldCode
        attributes = element.find('.//Attributes')
        if attributes is not None:
            for attr in attributes.findall('Attribute'):
                name_elem = attr.find('Name')
                value_elem = attr.find('Value')
                
                if name_elem is not None and value_elem is not None:
                    if name_elem.text == 'FieldCode':
                        fc_code = value_elem.text
                    elif name_elem.text == 'FieldName':
                        field_name = value_elem.text
                    elif name_elem.text == 'FieldDataType':
                        field_type = value_elem.text
        
        # 方法2: 从Name属性中查找FC Code
        if not fc_code:
            name_attr = element.get('Name')
            if name_attr and self._is_fc_code(name_attr):
                fc_code = name_attr
        
        # 方法3: 从ID属性中查找FC Code
        if not fc_code:
            id_attr = element.get('ID')
            if id_attr and self._is_fc_code(id_attr):
                fc_code = id_attr
        
        # 方法4: 从子元素Name中查找FC Code
        if not fc_code:
            name_elem = element.find('.//Name')
            if name_elem is not None and self._is_fc_code(name_elem.text):
                fc_code = name_elem.text
        
        if not fc_code:
            return None
        
        # 提取其他信息
        inner_value_elem = element.find('.//InnerValue')
        if inner_value_elem is not None:
            current_value = inner_value_elem.text or ""
        
        background_text_elem = element.find('.//BackgroundText')
        if background_text_elem is not None:
            background_text = background_text_elem.text or ""
        
        # 检查是否可编辑
        user_editable_elem = element.find('.//UserEditable')
        if user_editable_elem is not None:
            is_editable = user_editable_elem.text != 'false'
        
        content_readonly_elem = element.find('.//ContentReadonly')
        if content_readonly_elem is not None:
            is_editable = content_readonly_elem.text != 'True'
        
        # 确定字段分类
        category = self.fc_code_categories.get(fc_code, FCCodeType.MEDICAL_CONTENT)
        
        return FCCodeField(
            fc_code=fc_code,
            field_name=field_name or background_text or fc_code,
            field_type=field_type or "S1",
            element_type=element.tag.split('}')[-1] if '}' in element.tag else element.tag,
            is_editable=is_editable,
            is_required=self._is_required_field(fc_code),
            current_value=current_value,
            background_text=background_text,
            xml_element=element,
            category=category
        )
    
    def _is_fc_code(self, text: str) -> bool:
        """
        判断文本是否为FC Code格式
        
        Args:
            text: 待检查的文本
            
        Returns:
            bool: 是否为FC Code
        """
        if not text:
            return False
        return bool(re.match(r'^FC\d{10}$', text))
    
    def _is_required_field(self, fc_code: str) -> bool:
        """
        判断字段是否为必填字段
        
        Args:
            fc_code: FC Code
            
        Returns:
            bool: 是否必填
        """
        # 患者基本信息通常为必填
        required_codes = {
            "FC0000001099",  # 患者姓名
            "FC0000001108",  # 性别代码
            "FC0000001105",  # 年龄
            "FC0000001113",  # 主诉
            "FC0000000850",  # 现病史内容
        }
        return fc_code in required_codes
    
    def find_element_by_fc_code(self, root: ET.Element, fc_code: str) -> Optional[ET.Element]:
        """
        根据FC Code查找对应的XML元素
        
        Args:
            root: XML根元素
            fc_code: FC Code
            
        Returns:
            Optional[ET.Element]: 找到的XML元素，未找到返回None
        """
        # 查找策略1: 在Attributes中查找FieldCode
        for element in root.iter():
            attributes = element.find('.//Attributes')
            if attributes is not None:
                for attr in attributes.findall('Attribute'):
                    name_elem = attr.find('Name')
                    value_elem = attr.find('Value')
                    
                    if (name_elem is not None and value_elem is not None and 
                        name_elem.text == 'FieldCode' and value_elem.text == fc_code):
                        return element
        
        # 查找策略2: 在Name属性中查找
        for element in root.iter():
            name_elem = element.find('.//Name')
            if name_elem is not None and name_elem.text == fc_code:
                return element
        
        # 查找策略3: 在ID属性中查找
        for element in root.iter():
            if element.get('ID') == fc_code:
                return element
        
        return None
    
    def update_field_value(self, element: ET.Element, new_value: str) -> bool:
        """
        更新XML元素的值
        
        Args:
            element: XML元素
            new_value: 新值
            
        Returns:
            bool: 更新是否成功
        """
        try:
            # 更新InnerValue
            inner_value_elem = element.find('.//InnerValue')
            if inner_value_elem is not None:
                inner_value_elem.text = new_value
            
            # 更新XString元素的Text
            xstring_elem = element.find('.//XString/Text')
            if xstring_elem is not None:
                xstring_elem.text = new_value
            
            # 如果是XInputField，还需要更新XElements中的XString
            if 'XInputField' in element.tag:
                xelements = element.find('.//XElements')
                if xelements is not None:
                    for xstring in xelements.findall('.//XString'):
                        text_elem = xstring.find('Text')
                        if text_elem is not None:
                            text_elem.text = new_value
                            break
            
            return True
            
        except Exception as e:
            print(f"更新字段值失败: {e}")
            return False
    
    def get_editable_fields(self, fc_fields: Dict[str, FCCodeField]) -> Dict[str, FCCodeField]:
        """
        获取可编辑的字段
        
        Args:
            fc_fields: 所有FC Code字段
            
        Returns:
            Dict[str, FCCodeField]: 可编辑的字段
        """
        return {fc_code: field for fc_code, field in fc_fields.items() 
                if field.is_editable and field.category == FCCodeType.MEDICAL_CONTENT}
    
    def get_fields_by_category(self, fc_fields: Dict[str, FCCodeField], 
                              category: FCCodeType) -> Dict[str, FCCodeField]:
        """
        根据分类获取字段
        
        Args:
            fc_fields: 所有FC Code字段
            category: 字段分类
            
        Returns:
            Dict[str, FCCodeField]: 指定分类的字段
        """
        return {fc_code: field for fc_code, field in fc_fields.items() 
                if field.category == category}


class XMLTemplateProcessor:
    """XML模板处理器 - 完整的模板处理流程"""
    
    def __init__(self):
        self.fc_mapper = FCCodeMapper()
    
    def process_template(self, xml_content: str, patient_data: Dict[str, Any], 
                        llm_generated_content: Dict[str, str]) -> str:
        """
        处理XML模板，填充数据
        
        Args:
            xml_content: XML模板内容
            patient_data: 患者数据
            llm_generated_content: LLM生成的内容
            
        Returns:
            str: 填充后的XML内容
        """
        try:
            root = ET.fromstring(xml_content)
            fc_fields = self.fc_mapper.parse_xml_template(xml_content)
            
            # 填充患者基本信息
            self._fill_patient_info(root, fc_fields, patient_data)
            
            # 填充LLM生成的医疗内容
            self._fill_medical_content(root, fc_fields, llm_generated_content)
            
            # 填充系统信息
            self._fill_system_info(root, fc_fields)
            
            return ET.tostring(root, encoding='unicode')
            
        except Exception as e:
            raise ValueError(f"模板处理失败: {e}")
    
    def _fill_patient_info(self, root: ET.Element, fc_fields: Dict[str, FCCodeField], 
                          patient_data: Dict[str, Any]):
        """填充患者基本信息"""
        patient_mapping = {
            "FC0000001099": patient_data.get("name", ""),      # 患者姓名
            "FC0000001108": patient_data.get("gender", ""),    # 性别
            "FC0000001105": str(patient_data.get("age", "")),  # 年龄
            "FC0000001092": patient_data.get("bed_no", ""),    # 病床号
            "FC0000001114": patient_data.get("admission_no", ""),  # 住院号
            "FC0000001104": patient_data.get("department", ""),    # 科室名称
        }
        
        for fc_code, value in patient_mapping.items():
            if fc_code in fc_fields and value:
                element = self.fc_mapper.find_element_by_fc_code(root, fc_code)
                if element is not None:
                    self.fc_mapper.update_field_value(element, value)
    
    def _fill_medical_content(self, root: ET.Element, fc_fields: Dict[str, FCCodeField], 
                             llm_content: Dict[str, str]):
        """填充LLM生成的医疗内容"""
        for fc_code, content in llm_content.items():
            if fc_code in fc_fields and content:
                element = self.fc_mapper.find_element_by_fc_code(root, fc_code)
                if element is not None:
                    self.fc_mapper.update_field_value(element, content)
    
    def _fill_system_info(self, root: ET.Element, fc_fields: Dict[str, FCCodeField]):
        """填充系统信息"""
        from datetime import datetime
        
        system_mapping = {
            "FC0000015114": "鹤壁市人民医院",  # 医院名称
            "FC0000015208": "1",              # 住院次数
            "FC0000015219": "岁",             # 年龄单位
            "FC0000001100": datetime.now().strftime("%Y/%m/%d %H:%M:%S"),  # 记录日期时间
        }
        
        for fc_code, value in system_mapping.items():
            if fc_code in fc_fields:
                element = self.fc_mapper.find_element_by_fc_code(root, fc_code)
                if element is not None:
                    self.fc_mapper.update_field_value(element, value)


# 使用示例和测试代码
def demonstrate_fc_code_mapping():
    """演示FC Code映射机制"""
    
    # 示例XML片段（基于抓包数据）
    sample_xml = '''<?xml version="1.0" encoding="utf-8"?>
    <XTextDocument>
        <XElements>
            <Element xsi:type="XInputField">
                <Attributes>
                    <Attribute>
                        <Name>FieldCode</Name>
                        <Value>FC0000001099</Value>
                    </Attribute>
                    <Attribute>
                        <Name>FieldName</Name>
                        <Value>患者姓名</Value>
                    </Attribute>
                    <Attribute>
                        <Name>FieldDataType</Name>
                        <Value>S1</Value>
                    </Attribute>
                </Attributes>
                <Name>FC0000001099</Name>
                <InnerValue>程太生</InnerValue>
                <BackgroundText>患者姓名</BackgroundText>
                <UserEditable>false</UserEditable>
            </Element>
            <Element xsi:type="XInputField">
                <Attributes>
                    <Attribute>
                        <Name>FieldCode</Name>
                        <Value>FC0000001113</Value>
                    </Attribute>
                    <Attribute>
                        <Name>FieldName</Name>
                        <Value>主诉</Value>
                    </Attribute>
                </Attributes>
                <Name>FC0000001113</Name>
                <InnerValue>认知功能下降半年，加重1周</InnerValue>
                <BackgroundText>主诉在此次录入！！！</BackgroundText>
            </Element>
        </XElements>
    </XTextDocument>'''
    
    # 创建处理器
    processor = XMLTemplateProcessor()
    
    # 解析FC Code字段
    fc_fields = processor.fc_mapper.parse_xml_template(sample_xml)
    
    print("=== FC Code字段解析结果 ===")
    for fc_code, field in fc_fields.items():
        print(f"FC Code: {fc_code}")
        print(f"  字段名称: {field.field_name}")
        print(f"  字段类型: {field.field_type}")
        print(f"  元素类型: {field.element_type}")
        print(f"  是否可编辑: {field.is_editable}")
        print(f"  字段分类: {field.category.value}")
        print(f"  当前值: {field.current_value}")
        print(f"  背景文本: {field.background_text}")
        print()
    
    # 演示字段分类
    print("=== 按分类查看字段 ===")
    for category in FCCodeType:
        fields = processor.fc_mapper.get_fields_by_category(fc_fields, category)
        if fields:
            print(f"{category.value}类字段:")
            for fc_code, field in fields.items():
                print(f"  {fc_code}: {field.field_name}")
            print()


if __name__ == "__main__":
    demonstrate_fc_code_mapping()
